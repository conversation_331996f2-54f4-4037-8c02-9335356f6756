C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE SYSTEM
OBJECT MODULE PLACED IN .\Objects\system.obj
COMPILER INVOKED BY: D:\KeilC51\C51\BIN\C51.EXE ..\Libary\StdDriver\src\system.c LARGE OMF2 OPTIMIZE(8,SPEED) BROWSE INC
                    -DIR(..\Libary\Device\CMS8S6990\Include;..\Libary\Device\CMS8S6990;..\Libary\StdDriver\inc;..\Driver;..\code) DEBUG PRINT
                    -(.\Listings\system.lst) TABS(2) OBJECT(.\Objects\system.obj)

line level    source

   1          /*******************************************************************************
   2          * Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
   3          *
   4          * This software is owned and published by:
   5          * CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
   6          *
   7          * BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
   8          * BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
   9          *
  10          * This software contains source code for use with CMS
  11          * components. This software is licensed by CMS to be adapted only
  12          * for use in systems utilizing CMS components. CMS shall not be
  13          * responsible for misuse or illegal use of this software for devices not
  14          * supported herein. CMS is providing this software "AS IS" and will
  15          * not be responsible for issues arising from incorrect user implementation
  16          * of the software.
  17          *
  18          * This software may be replicated in part or whole for the licensed use,
  19          * with the restriction that this Disclaimer and Copyright notice must be
  20          * included with each copy of this software, whether used in part or whole,
  21          * at all times.
  22          */
  23          
  24          /****************************************************************************/
  25          /** \file system.c
  26          **
  27          **  
  28          **
  29          **  History:
  30          **  
  31          *****************************************************************************/
  32          /****************************************************************************/
  33          /*  include files
  34          *****************************************************************************/
  35          #include "system.h"
  36          
  37          /****************************************************************************/
  38          /*  Local pre-processor symbols/macros('#define')
  39          ****************************************************************************/
  40          
  41          /****************************************************************************/
  42          /*  Global variable definitions(declared in header file with 'extern')
  43          ****************************************************************************/
  44          
  45          /****************************************************************************/
  46          /*  Local type definitions('typedef')
  47          ****************************************************************************/
  48          
  49          /****************************************************************************/
  50          /*  Local variable  definitions('static')
  51          ****************************************************************************/
  52          
  53          /****************************************************************************/
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 2   

  54          /*  Local function prototypes('static')
  55          ****************************************************************************/
  56          
  57          /****************************************************************************/
  58          /*  Function implementation - global ('extern') and local('static')
  59          ****************************************************************************/
  60          
  61          /*****************************************************************************
  62           ** \brief   SYS_EnableLVD
  63           **      开启电压监测功能
  64           ** \param [in] none
  65           ** \return  none
  66           ** \note  
  67          *****************************************************************************/
  68          void SYS_EnableLVD(void)
  69          {
  70   1        LVDCON |= LVD_LVDCON_LVDEN_Msk;
  71   1      }
  72          /*****************************************************************************
  73           ** \brief   SYS_DisableLVD
  74           **       关闭电压监测功能
  75           ** \param [in] none
  76           ** \return  none
  77           ** \note  
  78          *****************************************************************************/
  79          void SYS_DisableLVD(void)
  80          {
  81   1        LVDCON &= ~(LVD_LVDCON_LVDEN_Msk);  
  82   1      }
  83          /*****************************************************************************
  84           ** \brief   SYS_ConfigLVD
  85           **      配置系统电压监测电压
  86           ** \param [in] LVDValue :(1)SYS_LVD_2_0V
  87           **             (2)SYS_LVD_2_2V
  88           **             (3)SYS_LVD_2_4V
  89           **             (4)SYS_LVD_2_7V
  90           **             (5)SYS_LVD_3_0V
  91           **             (6)SYS_LVD_3_7V
  92           **             (7)SYS_LVD_4_0V
  93           **             (7)SYS_LVD_4_3V
  94           ** \return  none
  95           ** \note  
  96          *****************************************************************************/
  97          void SYS_ConfigLVD(uint8_t LVDValue)
  98          {
  99   1        uint8_t Temp=0;
 100   1        
 101   1        Temp = LVDCON;
 102   1        Temp &= ~(LVD_LVDCON_LVDSEL_Msk);
 103   1        Temp |= LVDValue; 
 104   1        LVDCON = Temp;  
 105   1      }
 106          
 107          /*****************************************************************************
 108           ** \brief   SYS_EnableLVDInt
 109           **      开启电压监测中断功能
 110           ** \param [in] none
 111           ** \return  none
 112           ** \note  
 113          *****************************************************************************/
 114          void SYS_EnableLVDInt(void)
 115          {
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 3   

 116   1        LVDCON |= LVD_LVDCON_LVDINTE_Msk; 
 117   1      }
 118          /*****************************************************************************
 119           ** \brief   SYS_DisableLVDInt
 120           **       关闭电压监测中断功能
 121           ** \param [in] none
 122           ** \return  none
 123           ** \note  
 124          *****************************************************************************/
 125          void SYS_DisableLVDInt(void)
 126          {
 127   1        LVDCON &= ~(LVD_LVDCON_LVDINTE_Msk);  
 128   1      }
 129          
 130          /*****************************************************************************
 131           ** \brief   SYS_GetLVDIntFlag
 132           **      获取LVD中断标志位
 133           ** \param [in] none
 134           ** \return  0:无中断 1：有中断
 135           ** \note  
 136          *****************************************************************************/
 137          uint8_t SYS_GetLVDIntFlag(void)
 138          {
 139   1        return((LVDCON & LVD_LVDCON_LVDINTF_Msk)? 1:0);
 140   1      }
 141          
 142          /*****************************************************************************
 143           ** \brief   SYS_ClearLVDIntFlag
 144           **      清除LVD中断标志位
 145           ** \param [in] none
 146           ** \return  none
 147           ** \note  
 148          *****************************************************************************/
 149          void SYS_ClearLVDIntFlag(void)
 150          {
 151   1        LVDCON  &= ~(LVD_LVDCON_LVDINTF_Msk);
 152   1      }
 153          
 154          
 155          /*****************************************************************************
 156           ** \brief   SYS_EnableWDTReset
 157           **      使能WDT复位System
 158           ** \param [in] none
 159           **
 160           ** \return none
 161           ** \note
 162           *****************************************************************************/
 163          void SYS_EnableWDTReset(void)
 164          {
 165   1        if(EA==1) //操作TA时序时不允许被打断，因而需要关闭中断
 166   1        {
 167   2          EA=0;   //在CPU_WAITCLOCK选择1T的模式时，在EA=0后必须加nop,选择多T时不加。
 168   2          _nop_();
 169   2          TA = 0xAA;
 170   2          TA = 0x55;
 171   2          WDCON |= WDT_WDCON_WDTRE_Msk;
 172   2          EA=1;
 173   2        }
 174   1        else
 175   1        {
 176   2          TA = 0xAA;
 177   2          TA = 0x55;
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 4   

 178   2          WDCON |= WDT_WDCON_WDTRE_Msk;
 179   2        }
 180   1      }
 181          /*****************************************************************************
 182           ** \brief   SYS_DisableWDTReset
 183           **      关闭WDT复位System
 184           ** \param [in] none
 185           **
 186           ** \return none
 187           ** \note
 188           *****************************************************************************/
 189          void SYS_DisableWDTReset(void)
 190          {
 191   1        if(EA==1) //操作TA时序时不允许被打断，因而需要关闭中断
 192   1        {
 193   2          EA=0;   //在CPU_WAITCLOCK选择1T的模式时，在EA=0后必须加nop,选择多T时不加。
 194   2          _nop_();
 195   2          TA = 0xAA;
 196   2          TA = 0x55;
 197   2          WDCON &= ~(WDT_WDCON_WDTRE_Msk);
 198   2          EA=1;
 199   2        }
 200   1        else
 201   1        {
 202   2          TA = 0xAA;
 203   2          TA = 0x55;
 204   2          WDCON &= ~(WDT_WDCON_WDTRE_Msk);
 205   2        }
 206   1      }
 207          /*****************************************************************************
 208           ** \brief   SYS_GetWDTResetFlag
 209           **      获取WDT复位System的标志
 210           ** \param [in] none
 211           **
 212           ** \return 0：复位不由WDT溢出引起    1：复位由WDT溢出引起
 213           ** \note
 214           *****************************************************************************/
 215          uint8_t SYS_GetWDTResetFlag(void)
 216          {
 217   1        return((WDCON & WDT_WDCON_WDTRF_Msk)? 1:0);
 218   1      }
 219          /*****************************************************************************
 220           ** \brief   SYS_ClearWDTResetFlag
 221           **      清除WDT复位System的标志
 222           ** \param [in] none
 223           **
 224           ** \return   none
 225           ** \note
 226           *****************************************************************************/
 227          void SYS_ClearWDTResetFlag(void)
 228          {
 229   1        if(EA==1) //操作TA时序时不允许被打断，因而需要关闭中断
 230   1        {
 231   2          EA=0;   //在CPU_WAITCLOCK选择1T的模式时，在EA=0后必须加nop,选择多T时不加。
 232   2          _nop_();
 233   2          TA = 0xAA;
 234   2          TA = 0x55;
 235   2          WDCON &= ~(WDT_WDCON_WDTRF_Msk);  
 236   2          EA=1;
 237   2        }
 238   1        else
 239   1        {
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 5   

 240   2          TA = 0xAA;
 241   2          TA = 0x55;
 242   2          WDCON &= ~(WDT_WDCON_WDTRF_Msk);
 243   2      
 244   2        }
 245   1      }
 246          
 247          /*****************************************************************************
 248           ** \brief   SYS_EnableSoftwareReset
 249           **      使能软件复位System
 250           ** \param [in] none
 251           **
 252           ** \return none
 253           ** \note
 254           *****************************************************************************/
 255          void SYS_EnableSoftwareReset(void)
 256          {
 257   1        if(EA==1) //操作TA时序时不允许被打断，因而需要关闭中断
 258   1        {
 259   2          EA=0;   //在CPU_WAITCLOCK选择1T的模式时，在EA=0后必须加nop,选择多T时不加。
 260   2          _nop_();
 261   2          TA = 0xAA;
 262   2          TA = 0x55;
 263   2          WDCON |= WDT_WDCON_SWRST_Msk; 
 264   2          EA=1;
 265   2        }
 266   1        else
 267   1        {
 268   2          TA = 0xAA;
 269   2          TA = 0x55;
 270   2          WDCON |= WDT_WDCON_SWRST_Msk; 
 271   2        }
 272   1      }
 273          /*****************************************************************************
 274           ** \brief   SYS_DisableSoftwareReset
 275           **      关闭软件复位System
 276           ** \param [in] none
 277           **
 278           ** \return none
 279           ** \note
 280           *****************************************************************************/
 281          void SYS_DisableSoftwareReset(void)
 282          {
 283   1        if(EA==1) //操作TA时序时不允许被打断，因而需要关闭中断
 284   1        {
 285   2          EA=0;   //在CPU_WAITCLOCK选择1T的模式时，在EA=0后必须加nop,选择多T时不加。
 286   2          _nop_();
 287   2          TA = 0xAA;
 288   2          TA = 0x55;
 289   2          WDCON &= ~(WDT_WDCON_SWRST_Msk);  
 290   2          EA=1;
 291   2        }
 292   1        else
 293   1        {
 294   2          TA = 0xAA;
 295   2          TA = 0x55;
 296   2          WDCON &= ~(WDT_WDCON_SWRST_Msk);  
 297   2        }
 298   1      }
 299          /*****************************************************************************
 300           ** \brief   SYS_GetPowerOnResetFlag
 301           **      获取上电复位System的标志
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 6   

 302           ** \param [in] none
 303           **
 304           ** \return   0：复位不由上电复位引起    1：复位由上电复位引起
 305           ** \note
 306           *****************************************************************************/
 307          uint8_t SYS_GetPowerOnResetFlag(void)
 308          {
 309   1        return((WDCON & WDT_WDCON_PORF_Msk)? 1:0);
 310   1      }
 311          /*****************************************************************************
 312           ** \brief   SYS_ClearPowerOnResetFlag
 313           **      清除PowerOn复位System的标志
 314           ** \param [in] none
 315           **
 316           ** \return   none
 317           ** \note
 318           *****************************************************************************/
 319          void SYS_ClearPowerOnResetFlag(void)
 320          {
 321   1        WDCON &= ~(WDT_WDCON_PORF_Msk); 
 322   1      }
 323          
 324          
 325          /*****************************************************************************
 326           ** \brief   SYS_EnableWakeUp
 327           **      使能休眠唤醒
 328           ** \param [in] none
 329           **
 330           ** \return none
 331           ** \note  可由外部中断、GPIO中断唤醒
 332           *****************************************************************************/
 333          void SYS_EnableWakeUp(void)
 334          {
 335   1        PCON |= SYS_PCON_SWE_Msk;
 336   1      }
 337          /*****************************************************************************
 338           ** \brief   SYS_EnableWakeUp
 339           **      关闭休眠唤醒
 340           ** \param [in] none
 341           **
 342           ** \return none
 343           ** \note 只能由外部复位或者LVR复位唤醒
 344           *****************************************************************************/
 345          void SYS_DisableWakeUp(void)
 346          {
 347   1        PCON &= ~(SYS_PCON_SWE_Msk);
 348   1      }
 349          
 350          /*****************************************************************************
 351           ** \brief   SYS_EnterStop
 352           **      进入休眠中STOP状态
 353           ** \param [in] none
 354           **
 355           ** \return none
 356           ** \note 
 357           *****************************************************************************/
 358          void SYS_EnterStop(void)
 359          { 
 360   1        _nop_();
 361   1        _nop_();
 362   1        PCON |= SYS_PCON_STOP_Msk;
 363   1        _nop_();  
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 7   

 364   1        _nop_();
 365   1        _nop_();
 366   1        _nop_();
 367   1        _nop_();
 368   1        _nop_();  
 369   1      }
 370          /*****************************************************************************
 371           ** \brief   SYS_EnterIdle
 372           **      进入休眠模式中Idle状态
 373           ** \param [in] none
 374           **
 375           ** \return none
 376           ** \note   
 377           *****************************************************************************/
 378          void SYS_EnterIdle(void)
 379          {
 380   1        _nop_();
 381   1        _nop_();
 382   1        PCON |= SYS_PCON_IDLE_Msk;
 383   1        _nop_();  
 384   1        _nop_();
 385   1        _nop_();
 386   1        _nop_();
 387   1        _nop_();
 388   1        _nop_();
 389   1      }
 390          
 391          /*****************************************************************************
 392           ** \brief   SYS_EnableWakeUpTrig
 393           **      使能定时唤醒功能
 394           ** \param [in] none
 395           **
 396           ** \return none
 397           ** \note   
 398           *****************************************************************************/
 399          void SYS_EnableWakeUpTrig(void)
 400          {
 401   1        WUTCRH |= (1<<7);
 402   1      
 403   1      }
 404          /*****************************************************************************
 405           ** \brief   SYS_DisableWakeUpTrig
 406           **      关闭定时唤醒功能
 407           ** \param [in] none
 408           **
 409           ** \return none
 410           ** \note   
 411           *****************************************************************************/
 412          void SYS_DisableWakeUpTrig(void)
 413          {
 414   1        WUTCRH &= ~(1<<7);
 415   1      }
 416          
 417          /*****************************************************************************
 418           ** \brief   SYS_ConfigWUTCLK
 419           **     设置定时唤醒时钟
 420           ** \param [in] clkdiv: (1)WUT_CLK_DIV_1 
 421           **           (2)WUT_CLK_DIV_8
 422           **           (2)WUT_CLK_DIV_32
 423          **            (2)WUT_CLK_DIV_256
 424           ** \return none
 425           ** \note   
C51 COMPILER V9.60.0.0   SYSTEM                                                            07/12/2025 13:07:06 PAGE 8   

 426           *****************************************************************************/
 427          void SYS_ConfigWUTCLK(uint8_t clkdiv )
 428          {
 429   1        uint8_t Temp=0;
 430   1        
 431   1        Temp = WUTCRH;
 432   1        Temp &= ~(0x3<<4);
 433   1        Temp |= (clkdiv<<4);
 434   1        WUTCRH = Temp;  
 435   1      }
 436          
 437          /*****************************************************************************
 438           ** \brief   SYS_ConfigWUTTime
 439           **     设置定时唤醒时间
 440           ** \param [in] time: 12it（0x0 ~ 0xFFF）
 441           ** \return none
 442           ** \note   
 443           *****************************************************************************/
 444          void SYS_ConfigWUTTime(uint16_t time )
 445          {
 446   1        uint8_t Temp=0;
 447   1        
 448   1        Temp = WUTCRH;
 449   1        Temp &=0xf0;
 450   1        Temp |= time>>8;
 451   1        WUTCRH = Temp;
 452   1        
 453   1        WUTCRL = time;
 454   1      }
 455          


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    298    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
