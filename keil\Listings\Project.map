LX51 LINKER/LOCATER V4.66.97.0                                                          07/12/2025  13:07:08  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   00266AH   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000C6H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000007H.2 BIT
C:000000H   C:000000H   C:00FFFFH   000028H   CONST
I:000000H   I:000000H   I:00007FH   00000DH   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00000CH   000005H   BYTE   UNIT     DATA           _DATA_GROUP_
00000DH.0 00001FH.7 000013H.0 ---    ---      **GAP**
000020H.0 000024H.3 000004H.4 BIT    UNIT     BIT            ?BI?MAIN
000024H.4 000026H.0 000001H.5 BIT    UNIT     BIT            _BIT_GROUP_
000026H.1 000026H.5 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000026H.6 000027H.1 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000027H.2 000027H   000000H.6 ---    ---      **GAP**
000028H   000028H   000001H   BYTE   UNIT     IDATA          ?STACK
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 3



* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000026H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
000027H   000027H   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
000028H   000028H   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
000038H   00003AH   000003H   ---    ---      **GAP**
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   000042H   000005H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000062H   00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   000091H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000092H   000092H   000001H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   00091BH   000866H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
00091CH   00112DH   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
00112EH   0015B0H   000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
0015B1H   00171BH   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
00171CH   001856H   00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
001857H   001980H   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 4


001981H   001A99H   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
001A9AH   001BA9H   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
001BAAH   001CB2H   000109H   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
001CB3H   001D87H   0000D5H   BYTE   UNIT     CODE           ?C_INITSEG
001D88H   001E21H   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001E22H   001EB4H   000093H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001EB5H   001F2CH   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001F2DH   001FA4H   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001FA5H   002017H   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
002018H   002087H   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
002088H   0020F4H   00006DH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
0020F5H   002153H   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
002154H   0021B0H   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
0021B1H   00220BH   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
00220CH   00225AH   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
00225BH   0022A6H   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
0022A7H   0022F0H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
0022F1H   002337H   000047H   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
002338H   002376H   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
002377H   0023B0H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
0023B1H   0023E5H   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
0023E6H   002416H   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
002417H   002443H   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
002444H   00246DH   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
00246EH   002496H   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
002497H   0024BDH   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
0024BEH   0024E2H   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
0024E3H   002507H   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
002508H   002527H   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
002528H   002547H   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
002548H   002567H   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
002568H   002586H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
002587H   0025A5H   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
0025A6H   0025C3H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
0025C4H   0025E0H   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
0025E1H   0025FAH   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
0025FBH   002610H   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
002611H   002624H   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
002625H   002637H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
002638H   00264AH   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
00264BH   00265DH   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
00265EH   00266EH   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
00266FH   002677H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
002678H   002680H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
002681H   002689H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
00268AH   002691H   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
002692H   002698H   000007H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
002699H   00269EH   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
00269FH   0026A4H   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
0026A5H   0026BFH   00001BH   BYTE   UNIT     CONST          ?CO?ADC_USED
0026C0H   0026CCH   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   00004FH   000050H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000050H   00007AH   00002BH   BYTE   UNIT     XDATA          ?XD?MAIN
00007BH   00009BH   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
00009CH   0000B0H   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
0000B1H   0000BBH   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
0000BCH   0000C1H   000006H   BYTE   UNIT     XDATA          ?XD?KEY
0000C2H   0000C5H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 5


   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 7


   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
   *DEL*:           000002H   BYTE   UNIT     XDATA          ?XD?RESTORE_DLY?MAIN



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP  START  STOP
===================================================================================
?C_C51STARTUP                                 ----- -----  ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     24H.4 24H.7  ----- -----  0000H 0010H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> _STORE_DLY/MAIN
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----  ----- -----

_DELAY1MS/MAIN                                ----- -----  ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 8


_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  ----- -----  0011H 0016H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  ----- -----  0011H 0016H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 9


KEY_SCAN/KEY                                  ----- -----  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  ----- -----  0011H 001EH
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  ----- -----  001FH 0023H

BATTERY_CHECK/MAIN                            ----- -----  ----- -----  ----- -----
  +--> _ADC/ADC_USED

_ADC/ADC_USED                                 ----- -----  ----- -----  0011H 001FH
  +--> _ADC_ENABLECHANNEL/ADC
  +--> ADC_GETADCRESULT/ADC
  +--> PRINTF/PRINTF

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----  ----- -----

PRINTF/PRINTF                                 25H.0 26H.0  0008H 000CH  0020H 004FH
  +--> _PUTCHAR/UART_INIT

_PUTCHAR/UART_INIT                            ----- -----  ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----  ----- -----
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  ----- -----  0011H 0012H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

FLASH_UNLOCK/FLASH                            ----- -----  ----- -----  ----- -----

_FLASH_ERASE/FLASH                            ----- -----  ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----  ----- -----

?C_INITSEG                                    ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 10


*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

LSE_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 11



TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
      02000020H   XDATA    ---       ?_PRINTF?BYTE
      02000020H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      0200001FH   XDATA    BYTE      ?_UART_Send_String?BYTE
      01001108H   CODE     ---       ?C?CCASE
      01000F03H   CODE     ---       ?C?CLDOPTR
      01000EEAH   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      01000EC4H   CODE     ---       ?C?COPY
      01000F42H   CODE     ---       ?C?CSTOPTR
      01000F30H   CODE     ---       ?C?CSTPTR
      01000ACCH   CODE     ---       ?C?FCASTC
      01000AC7H   CODE     ---       ?C?FCASTI
      01000AC2H   CODE     ---       ?C?FCASTL
      01000C93H   CODE     ---       ?C?FPADD
      01000B87H   CODE     ---       ?C?FPCONVERT
      01000A25H   CODE     ---       ?C?FPDIV
      01000B00H   CODE     ---       ?C?FPGETOPN2
      0100091CH   CODE     ---       ?C?FPMUL
      01000B35H   CODE     ---       ?C?FPNANRESULT
      01000B3FH   CODE     ---       ?C?FPOVERFLOW
      01000B17H   CODE     ---       ?C?FPRESULT
      01000B2BH   CODE     ---       ?C?FPRESULT2
      01000B4AH   CODE     ---       ?C?FPROUND
      01000C8FH   CODE     ---       ?C?FPSUB
      01000B3CH   CODE     ---       ?C?FPUNDERFLOW
      01000DB4H   CODE     ---       ?C?FTNPWR
      01000FB9H   CODE     ---       ?C?ILDIX
      0100109DH   CODE     ---       ?C?LNEG
      010010B7H   CODE     ---       ?C?LSTKXDATA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 12


      010010ABH   CODE     ---       ?C?LSTXDATA
      010010E8H   CODE     ---       ?C?PLDIXDATA
      010010FFH   CODE     ---       ?C?PSTXDATA
      01000F64H   CODE     ---       ?C?UIDIV
      0100100BH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      01001DDDH   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      0100171CH   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      0100266FH   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      010025FBH   CODE     ---       _ADC_ConfigRunMode
      010025A6H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      01002548H   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      010023E6H   CODE     ---       _FLASH_Erase
*DEL*:00000000H   CODE     ---       _FLASH_Read
      010023B1H   CODE     ---       _FLASH_Write
*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001FA5H   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 13


      010022F1H   CODE     ---       _Key_Function_Switch_System
      01001E22H   CODE     ---       _Motor_Step_Control
      01001199H   CODE     ---       _PRINTF
      01000056H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
      01001193H   CODE     ---       _SPRINTF
      01002417H   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      0100225BH   CODE     ---       _TMR_ConfigRunMode
      010022A7H   CODE     ---       _TMR_ConfigTimerClk
      01002497H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      010025C4H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      01002568H   CODE     ---       _TMR_Start
*DEL*:00000000H   CODE     ---       _TMR_Stop
      01002587H   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      01002678H   CODE     ---       _UART_ConfigBRTClk
      01002681H   CODE     ---       _UART_ConfigBRTPeriod
      010020F5H   CODE     ---       _UART_ConfigRunMode
      01002508H   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      01002625H   CODE     ---       _UART_EnableDoubleFrequency
      0100265EH   CODE     ---       _UART_EnableInt
      01002638H   CODE     ---       _UART_EnableReceive
      01002611H   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      010024E3H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01002154H   CODE     ---       _UART_Send_String
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      0100002AH   CODE     ---       ACMP_IRQHandler
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 14


      010025E1H   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      010024BEH   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000031H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.2 BIT      BIT       auto_rotate_flash
      02000060H   XDATA    WORD      auto_rotate_flash_timer
      00000021H.6 BIT      BIT       auto_rotate_mode
      00000020H.6 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000021H.7 BIT      BIT       batlow1
      02000077H   XDATA    BYTE      batlow1_cnt
      02000065H   XDATA    BYTE      batlow_cnt
      020000ACH   XDATA    WORD      Battery_ADC_Wait_Time
      01002088H   CODE     ---       Battery_Check
      02000078H   XDATA    WORD      BatV
      00000022H.7 BIT      BIT       Bit_1_ms_Buff
      00000023H.7 BIT      BIT       Bit_N_ms_Buff
      00000023H.3 BIT      BIT       Bit_Toggle
*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000026H.7 BIT      BIT       Center_Line_Control
      00000021H.5 BIT      BIT       Charg_State_Buff
      00000023H.1 BIT      BIT       charge_flash
      02000052H   XDATA    WORD      charge_flash_cnt
      00000022H.5 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      0100269FH   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      02000066H   XDATA    INT       Count_1_Degree_Pulse
      020000A7H   XDATA    INT       Count_Toggle
*SFR* 000000D0H.7 DATA     BIT       CY
      0200009BH   XDATA    BYTE      Data_Length
      00000023H.2 BIT      BIT       Delay_Open
      00000027H.1 BIT      BIT       Delay_Over
      020000A9H   XDATA    WORD      Delay_Time
      020000A5H   XDATA    WORD      Delay_Time_Count
      00000024H.0 BIT      BIT       direction_changed
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 15


      0200006CH   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000030H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000027H.0 BIT      BIT       Get_String_Buff
      020000ABH   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      01002018H   CODE     ---       GPIO_Config
      0100264BH   CODE     ---       GPIO_Key_Interrupt_Config
      01000036H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      0200009EH   XDATA    BYTE      K1_cnt
      00000020H.7 BIT      BIT       K1_cnt_EN
      020000BDH   XDATA    BYTE      K1_Count
      00000026H.1 BIT      BIT       K1_Press
      0200009FH   XDATA    BYTE      K2_cnt
      00000021H.0 BIT      BIT       K2_cnt_EN
      020000BEH   XDATA    BYTE      K2_Count
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 16


      00000023H.4 BIT      BIT       k2_long_press_detected
      02000068H   XDATA    WORD      k2_long_press_timer
      00000026H.2 BIT      BIT       K2_Press
      00000022H.0 BIT      BIT       k2_released
      020000A0H   XDATA    BYTE      K3_cnt
      00000021H.1 BIT      BIT       K3_cnt_EN
      020000BFH   XDATA    BYTE      K3_Count
      00000026H.3 BIT      BIT       K3_Press
      00000022H.1 BIT      BIT       k3_released
      020000C0H   XDATA    BYTE      K4_Count
      00000026H.4 BIT      BIT       K4_Press
      020000C1H   XDATA    BYTE      K5_Count
      00000026H.5 BIT      BIT       K5_Press
      0200006AH   XDATA    WORD      key1_duration
      00000022H.2 BIT      BIT       key1_handle
      00000023H.5 BIT      BIT       key1_long_started
      02000058H   XDATA    WORD      key1_press_time
      00000022H.6 BIT      BIT       key1_pressed
      0200006EH   XDATA    WORD      key3_duration
      00000022H.3 BIT      BIT       key3_handle
      00000023H.6 BIT      BIT       key3_long_started
      0200005AH   XDATA    WORD      key3_press_time
      00000023H.0 BIT      BIT       key3_pressed
      020000BCH   XDATA    BYTE      Key_Buff
      01002377H   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010015B1H   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      01001857H   CODE     ---       Key_Scan
      00000024H.1 BIT      BIT       key_short_press_mode
      02000076H   XDATA    BYTE      last_direction
      01001A9AH   CODE     ---       LED_Control
      00000021H.4 BIT      BIT       led_flash_state
      02000063H   XDATA    WORD      led_flash_timer
      00000022H.4 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000062H   XDATA    BYTE      ledonoff1_cnt
      0200007AH   XDATA    BYTE      ledonoff_cnt
      00000024H.2 BIT      BIT       longhit
      0200009CH   XDATA    WORD      longhit_cnt
      01000029H   CODE     ---       LSE_IRQHandler
      01000028H   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      020000B0H   XDATA    BYTE      Motor_Direction_Data
      00000020H.5 BIT      BIT       MOTOR_RUNNING_FLAG
      020000A3H   XDATA    WORD      Motor_Speed_Data
      00000021H.3 BIT      BIT       need_led_flash
      020000A1H   XDATA    INT       Num
      020000C2H   XDATA    INT       Num_Forward_Pulse
      020000C4H   XDATA    INT       Num_Reverse_Pulse
      02000073H   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 17


*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000026H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001EB5H   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001F2DH   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      01000027H   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000026H.6 BIT      BIT       Power_count_clean
      020000AEH   XDATA    WORD      Power_Off_Wait_Time
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
*DEL*:00000000H   CODE     ---       Restore_dly
      01002699H   CODE     ---       Return_UART_Data_Length
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 18


*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000070H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.3 BIT      BIT       speedup
      0200005CH   XDATA    WORD      speedup_cnt
      01000037H   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000086H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      02000075H   XDATA    BYTE      System_Mode_Before_Charge
      02000072H   XDATA    BYTE      System_Mode_Data
      02000054H   XDATA    DWORD     Systemclock
*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      01001BAAH   CODE     ---       Timer0_IRQHandler
      01002692H   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
      0100002EH   CODE     ---       Timer3_IRQHandler
      0100002FH   CODE     ---       Timer4_IRQHandler
      0200005EH   XDATA    WORD      timer_1ms_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 19


*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      01002444H   CODE     ---       TMR0_Config
      0100246EH   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      010021B1H   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      0100220CH   CODE     ---       UART_0_Config
      01002338H   CODE     ---       UART_1_Config
      01002528H   CODE     ---       UART_Data_Init
      01001981H   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      0100268AH   CODE     ---       UART_EnableBRT
      0200007BH   XDATA    ---       UART_Get_String
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000032H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001D8BH   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 20


      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      01001D88H   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      01001D96H   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      01001D88H   LINE      CODE     ---       #133
      01001D8AH   LINE      CODE     ---       #134
      01001D8BH   LINE      CODE     ---       #135
      01001D8CH   LINE      CODE     ---       #136
      01001D8EH   LINE      CODE     ---       #140
      01001D91H   LINE      CODE     ---       #141
      01001D93H   LINE      CODE     ---       #145
      01001D95H   LINE      CODE     ---       #147
      01001D96H   LINE      CODE     ---       #148
      01001D97H   LINE      CODE     ---       #149
      01001D98H   LINE      CODE     ---       #150
      01001D9AH   LINE      CODE     ---       #151
      01001D9CH   LINE      CODE     ---       #185
      01001D9FH   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      0100266FH   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      010024BEH   PUBLIC    CODE     ---       ADC_GetADCResult
      010025A6H   PUBLIC    CODE     ---       _ADC_EnableChannel
      010025FBH   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 21


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 22


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 23


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      010025FBH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      010025FBH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010025FBH   LINE      CODE     ---       #88
      010025FBH   LINE      CODE     ---       #89
      010025FBH   LINE      CODE     ---       #90
      010025FBH   LINE      CODE     ---       #92
      010025FDH   LINE      CODE     ---       #93
      01002600H   LINE      CODE     ---       #94
      01002601H   LINE      CODE     ---       #95
      01002603H   LINE      CODE     ---       #97
      01002605H   LINE      CODE     ---       #98
      01002609H   LINE      CODE     ---       #99
      0100260EH   LINE      CODE     ---       #100
      01002610H   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      010025A6H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      010025A6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010025A6H   LINE      CODE     ---       #154
      010025A6H   LINE      CODE     ---       #155
      010025A6H   LINE      CODE     ---       #156
      010025A6H   LINE      CODE     ---       #158
      010025A8H   LINE      CODE     ---       #159
      010025ACH   LINE      CODE     ---       #160
      010025B5H   LINE      CODE     ---       #161
      010025B7H   LINE      CODE     ---       #163
      010025B9H   LINE      CODE     ---       #164
      010025BDH   LINE      CODE     ---       #165
      010025C1H   LINE      CODE     ---       #166
      010025C3H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 24


      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      010024BEH   BLOCK     CODE     ---       LVL=0
      010024BEH   LINE      CODE     ---       #258
      010024BEH   LINE      CODE     ---       #259
      010024BEH   LINE      CODE     ---       #260
      010024C5H   LINE      CODE     ---       #261
      010024C5H   LINE      CODE     ---       #262
      010024D8H   LINE      CODE     ---       #263
      010024D8H   LINE      CODE     ---       #264
      010024E2H   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      0100266FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      0100266FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100266FH   LINE      CODE     ---       #344
      0100266FH   LINE      CODE     ---       #345
      0100266FH   LINE      CODE     ---       #346
      0100266FH   LINE      CODE     ---       #348
      01002673H   LINE      CODE     ---       #349
      01002675H   LINE      CODE     ---       #350
      01002676H   LINE      CODE     ---       #351
      01002677H   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 25


      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 26


      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 27


      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 28



      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 29


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 30


      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000086H   PUBLIC    CODE     ---       SYS_EnterStop
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 31


      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 32


      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 33


      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #358
      01000086H   LINE      CODE     ---       #359
      01000086H   LINE      CODE     ---       #360
      01000087H   LINE      CODE     ---       #361
      01000088H   LINE      CODE     ---       #362
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 34


      0100008BH   LINE      CODE     ---       #363
      0100008CH   LINE      CODE     ---       #364
      0100008DH   LINE      CODE     ---       #365
      0100008EH   LINE      CODE     ---       #366
      0100008FH   LINE      CODE     ---       #367
      01000090H   LINE      CODE     ---       #368
      01000091H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      01002568H   PUBLIC    CODE     ---       _TMR_Start
      010025C4H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      01002497H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      010022A7H   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      0100225BH   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 35


      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 36


      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 37


      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100225BH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      0100225BH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100225BH   LINE      CODE     ---       #74
      0100225BH   LINE      CODE     ---       #75
      0100225BH   LINE      CODE     ---       #76
      0100225BH   LINE      CODE     ---       #78
      0100226AH   LINE      CODE     ---       #79
      0100226AH   LINE      CODE     ---       #80
      0100226AH   LINE      CODE     ---       #81
      0100226CH   LINE      CODE     ---       #82
      01002270H   LINE      CODE     ---       #83
      01002276H   LINE      CODE     ---       #84
      01002276H   LINE      CODE     ---       #85
      01002278H   LINE      CODE     ---       #86
      01002278H   LINE      CODE     ---       #87
      0100227AH   LINE      CODE     ---       #88
      0100227EH   LINE      CODE     ---       #89
      0100228BH   LINE      CODE     ---       #90
      0100228DH   LINE      CODE     ---       #91
      0100228EH   LINE      CODE     ---       #92
      0100228EH   LINE      CODE     ---       #93
      01002290H   LINE      CODE     ---       #94
      01002293H   LINE      CODE     ---       #95
      01002294H   LINE      CODE     ---       #96
      01002296H   LINE      CODE     ---       #97
      01002297H   LINE      CODE     ---       #98
      01002297H   LINE      CODE     ---       #99
      01002299H   LINE      CODE     ---       #100
      0100229DH   LINE      CODE     ---       #101
      010022A4H   LINE      CODE     ---       #102
      010022A6H   LINE      CODE     ---       #103
      010022A6H   LINE      CODE     ---       #104
      010022A6H   LINE      CODE     ---       #105
      010022A6H   LINE      CODE     ---       #106
      010022A6H   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      010022A7H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      010022A7H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022A7H   LINE      CODE     ---       #117
      010022A7H   LINE      CODE     ---       #118
      010022A7H   LINE      CODE     ---       #119
      010022A7H   LINE      CODE     ---       #121
      010022B6H   LINE      CODE     ---       #122
      010022B6H   LINE      CODE     ---       #123
      010022B6H   LINE      CODE     ---       #124
      010022B8H   LINE      CODE     ---       #125
      010022BCH   LINE      CODE     ---       #126
      010022C2H   LINE      CODE     ---       #127
      010022C2H   LINE      CODE     ---       #128
      010022C4H   LINE      CODE     ---       #129
      010022C4H   LINE      CODE     ---       #130
      010022C6H   LINE      CODE     ---       #131
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 38


      010022CAH   LINE      CODE     ---       #132
      010022CFH   LINE      CODE     ---       #133
      010022D1H   LINE      CODE     ---       #134
      010022D2H   LINE      CODE     ---       #135
      010022D2H   LINE      CODE     ---       #136
      010022D4H   LINE      CODE     ---       #137
      010022D8H   LINE      CODE     ---       #138
      010022DDH   LINE      CODE     ---       #139
      010022DDH   LINE      CODE     ---       #140
      010022DFH   LINE      CODE     ---       #141
      010022DFH   LINE      CODE     ---       #142
      010022E1H   LINE      CODE     ---       #143
      010022E5H   LINE      CODE     ---       #144
      010022EEH   LINE      CODE     ---       #145
      010022F0H   LINE      CODE     ---       #146
      010022F0H   LINE      CODE     ---       #147
      010022F0H   LINE      CODE     ---       #148
      010022F0H   LINE      CODE     ---       #149
      010022F0H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      01002497H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      01002497H   LINE      CODE     ---       #160
      01002497H   LINE      CODE     ---       #161
      01002497H   LINE      CODE     ---       #162
      010024A6H   LINE      CODE     ---       #163
      010024A6H   LINE      CODE     ---       #164
      010024A6H   LINE      CODE     ---       #165
      010024A8H   LINE      CODE     ---       #166
      010024AAH   LINE      CODE     ---       #167
      010024ABH   LINE      CODE     ---       #168
      010024ABH   LINE      CODE     ---       #169
      010024ADH   LINE      CODE     ---       #170
      010024AFH   LINE      CODE     ---       #171
      010024B0H   LINE      CODE     ---       #172
      010024B0H   LINE      CODE     ---       #173
      010024B2H   LINE      CODE     ---       #174
      010024B4H   LINE      CODE     ---       #175
      010024B5H   LINE      CODE     ---       #176
      010024B5H   LINE      CODE     ---       #177
      010024B9H   LINE      CODE     ---       #178
      010024BDH   LINE      CODE     ---       #179
      010024BDH   LINE      CODE     ---       #180
      010024BDH   LINE      CODE     ---       #181
      010024BDH   LINE      CODE     ---       #182
      010024BDH   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      010025C4H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010025C4H   LINE      CODE     ---       #256
      010025C4H   LINE      CODE     ---       #257
      010025C4H   LINE      CODE     ---       #258
      010025D3H   LINE      CODE     ---       #259
      010025D3H   LINE      CODE     ---       #260
      010025D3H   LINE      CODE     ---       #261
      010025D5H   LINE      CODE     ---       #262
      010025D6H   LINE      CODE     ---       #263
      010025D6H   LINE      CODE     ---       #264
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 39


      010025D8H   LINE      CODE     ---       #265
      010025D9H   LINE      CODE     ---       #266
      010025D9H   LINE      CODE     ---       #267
      010025DCH   LINE      CODE     ---       #268
      010025DDH   LINE      CODE     ---       #269
      010025DDH   LINE      CODE     ---       #270
      010025E0H   LINE      CODE     ---       #271
      010025E0H   LINE      CODE     ---       #272
      010025E0H   LINE      CODE     ---       #273
      010025E0H   LINE      CODE     ---       #274
      010025E0H   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      01002568H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002568H   LINE      CODE     ---       #368
      01002568H   LINE      CODE     ---       #369
      01002568H   LINE      CODE     ---       #370
      01002577H   LINE      CODE     ---       #371
      01002577H   LINE      CODE     ---       #372
      01002577H   LINE      CODE     ---       #373
      0100257AH   LINE      CODE     ---       #374
      0100257BH   LINE      CODE     ---       #375
      0100257BH   LINE      CODE     ---       #376
      0100257EH   LINE      CODE     ---       #377
      0100257FH   LINE      CODE     ---       #378
      0100257FH   LINE      CODE     ---       #379
      01002582H   LINE      CODE     ---       #380
      01002583H   LINE      CODE     ---       #381
      01002583H   LINE      CODE     ---       #382
      01002586H   LINE      CODE     ---       #383
      01002586H   LINE      CODE     ---       #384
      01002586H   LINE      CODE     ---       #385
      01002586H   LINE      CODE     ---       #386
      01002586H   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 40


      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      01002681H   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      01002678H   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      0100268AH   PUBLIC    CODE     ---       UART_EnableBRT
      01002611H   PUBLIC    CODE     ---       _UART_GetBuff
      01002587H   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      010024E3H   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      0100265EH   PUBLIC    CODE     ---       _UART_EnableInt
      01002638H   PUBLIC    CODE     ---       _UART_EnableReceive
      01002625H   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      010020F5H   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 41


      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 42


      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 43



      010020F5H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      010020F5H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010020F5H   LINE      CODE     ---       #70
      010020F5H   LINE      CODE     ---       #71
      010020F5H   LINE      CODE     ---       #72
      010020F5H   LINE      CODE     ---       #74
      010020F8H   LINE      CODE     ---       #75
      010020F8H   LINE      CODE     ---       #76
      010020FAH   LINE      CODE     ---       #77
      010020FEH   LINE      CODE     ---       #78
      01002105H   LINE      CODE     ---       #79
      01002107H   LINE      CODE     ---       #81
      0100210AH   LINE      CODE     ---       #82
      01002116H   LINE      CODE     ---       #83
      01002116H   LINE      CODE     ---       #84
      01002116H   LINE      CODE     ---       #85
      01002116H   LINE      CODE     ---       #86
      01002116H   LINE      CODE     ---       #87
      01002119H   LINE      CODE     ---       #88
      0100211BH   LINE      CODE     ---       #89
      0100211BH   LINE      CODE     ---       #90
      0100211EH   LINE      CODE     ---       #91
      01002120H   LINE      CODE     ---       #92
      01002120H   LINE      CODE     ---       #93
      01002123H   LINE      CODE     ---       #94
      01002123H   LINE      CODE     ---       #95
      01002123H   LINE      CODE     ---       #96
      01002123H   LINE      CODE     ---       #97
      01002123H   LINE      CODE     ---       #99
      01002123H   LINE      CODE     ---       #100
      01002128H   LINE      CODE     ---       #101
      01002128H   LINE      CODE     ---       #102
      0100212AH   LINE      CODE     ---       #103
      0100212EH   LINE      CODE     ---       #104
      01002137H   LINE      CODE     ---       #105
      01002139H   LINE      CODE     ---       #107
      0100213CH   LINE      CODE     ---       #108
      01002148H   LINE      CODE     ---       #109
      01002148H   LINE      CODE     ---       #110
      01002148H   LINE      CODE     ---       #111
      01002148H   LINE      CODE     ---       #112
      01002148H   LINE      CODE     ---       #113
      0100214BH   LINE      CODE     ---       #114
      0100214CH   LINE      CODE     ---       #115
      0100214CH   LINE      CODE     ---       #116
      0100214FH   LINE      CODE     ---       #117
      01002150H   LINE      CODE     ---       #118
      01002150H   LINE      CODE     ---       #119
      01002153H   LINE      CODE     ---       #120
      01002153H   LINE      CODE     ---       #121
      01002153H   LINE      CODE     ---       #122
      01002153H   LINE      CODE     ---       #123
      01002153H   LINE      CODE     ---       #124
      01002153H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      01002625H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002625H   LINE      CODE     ---       #133
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 44


      01002625H   LINE      CODE     ---       #134
      01002625H   LINE      CODE     ---       #135
      0100262BH   LINE      CODE     ---       #136
      0100262BH   LINE      CODE     ---       #137
      0100262EH   LINE      CODE     ---       #138
      0100262EH   LINE      CODE     ---       #139
      01002634H   LINE      CODE     ---       #140
      01002634H   LINE      CODE     ---       #141
      01002637H   LINE      CODE     ---       #142
      01002637H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002638H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002638H   LINE      CODE     ---       #280
      01002638H   LINE      CODE     ---       #281
      01002638H   LINE      CODE     ---       #282
      0100263EH   LINE      CODE     ---       #283
      0100263EH   LINE      CODE     ---       #284
      01002641H   LINE      CODE     ---       #285
      01002641H   LINE      CODE     ---       #286
      01002647H   LINE      CODE     ---       #287
      01002647H   LINE      CODE     ---       #288
      0100264AH   LINE      CODE     ---       #289
      0100264AH   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100265EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100265EH   LINE      CODE     ---       #317
      0100265EH   LINE      CODE     ---       #318
      0100265EH   LINE      CODE     ---       #319
      01002664H   LINE      CODE     ---       #320
      01002664H   LINE      CODE     ---       #321
      01002666H   LINE      CODE     ---       #322
      01002666H   LINE      CODE     ---       #323
      0100266CH   LINE      CODE     ---       #324
      0100266CH   LINE      CODE     ---       #325
      0100266EH   LINE      CODE     ---       #326
      0100266EH   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010024E3H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      010024E3H   LINE      CODE     ---       #353
      010024E5H   LINE      CODE     ---       #354
      010024E5H   LINE      CODE     ---       #355
      010024EBH   LINE      CODE     ---       #356
      010024EBH   LINE      CODE     ---       #357
      010024F5H   LINE      CODE     ---       #358
      010024F5H   LINE      CODE     ---       #359
      010024FBH   LINE      CODE     ---       #360
      010024FBH   LINE      CODE     ---       #361
      01002505H   LINE      CODE     ---       #362
      01002505H   LINE      CODE     ---       #363
      01002507H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      01002587H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002587H   BLOCK     CODE     NEAR LAB  LVL=1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 45


      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002587H   LINE      CODE     ---       #373
      01002587H   LINE      CODE     ---       #374
      01002587H   LINE      CODE     ---       #377
      0100258DH   LINE      CODE     ---       #378
      0100258DH   LINE      CODE     ---       #379
      0100258FH   LINE      CODE     ---       #380
      01002592H   LINE      CODE     ---       #381
      01002596H   LINE      CODE     ---       #382
      01002596H   LINE      CODE     ---       #383
      0100259CH   LINE      CODE     ---       #384
      0100259CH   LINE      CODE     ---       #385
      0100259EH   LINE      CODE     ---       #386
      010025A1H   LINE      CODE     ---       #387
      010025A5H   LINE      CODE     ---       #388
      010025A5H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      01002611H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002611H   LINE      CODE     ---       #443
      01002611H   LINE      CODE     ---       #444
      01002611H   LINE      CODE     ---       #445
      01002617H   LINE      CODE     ---       #446
      01002617H   LINE      CODE     ---       #447
      0100261AH   LINE      CODE     ---       #448
      0100261AH   LINE      CODE     ---       #449
      01002622H   LINE      CODE     ---       #450
      01002622H   LINE      CODE     ---       #451
      01002624H   LINE      CODE     ---       #452
      01002624H   LINE      CODE     ---       #453
      01002624H   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100268AH   BLOCK     CODE     ---       LVL=0
      0100268AH   LINE      CODE     ---       #534
      0100268AH   LINE      CODE     ---       #535
      0100268AH   LINE      CODE     ---       #536
      01002691H   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      01002678H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      01002678H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002678H   LINE      CODE     ---       #544
      01002678H   LINE      CODE     ---       #545
      01002678H   LINE      CODE     ---       #546
      01002678H   LINE      CODE     ---       #548
      0100267CH   LINE      CODE     ---       #549
      0100267EH   LINE      CODE     ---       #550
      0100267FH   LINE      CODE     ---       #551
      01002680H   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 46


      01002681H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      01002681H   LINE      CODE     ---       #560
      01002681H   LINE      CODE     ---       #561
      01002681H   LINE      CODE     ---       #562
      01002686H   LINE      CODE     ---       #563
      01002689H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 47


      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 48


      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      010023E6H   PUBLIC    CODE     ---       _FLASH_Erase
      010023B1H   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 49


      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 50


      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 51


      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      010023B1H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 52


      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      010023B1H   LINE      CODE     ---       #95
      010023B3H   LINE      CODE     ---       #96
      010023B3H   LINE      CODE     ---       #97
      010023B7H   LINE      CODE     ---       #98
      010023B9H   LINE      CODE     ---       #99
      010023BCH   LINE      CODE     ---       #101
      010023BFH   LINE      CODE     ---       #102
      010023BFH   LINE      CODE     ---       #103
      010023C1H   LINE      CODE     ---       #104
      010023C2H   LINE      CODE     ---       #105
      010023C7H   LINE      CODE     ---       #106
      010023C8H   LINE      CODE     ---       #107
      010023C9H   LINE      CODE     ---       #108
      010023CAH   LINE      CODE     ---       #109
      010023CBH   LINE      CODE     ---       #110
      010023CCH   LINE      CODE     ---       #111
      010023CDH   LINE      CODE     ---       #112
      010023D2H   LINE      CODE     ---       #113
      010023D4H   LINE      CODE     ---       #114
      010023D5H   LINE      CODE     ---       #116
      010023D5H   LINE      CODE     ---       #117
      010023DAH   LINE      CODE     ---       #118
      010023DBH   LINE      CODE     ---       #119
      010023DCH   LINE      CODE     ---       #120
      010023DDH   LINE      CODE     ---       #121
      010023DEH   LINE      CODE     ---       #122
      010023DFH   LINE      CODE     ---       #123
      010023E0H   LINE      CODE     ---       #124
      010023E5H   LINE      CODE     ---       #125
      010023E5H   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr

      010023E6H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      010023E6H   LINE      CODE     ---       #179
      010023E8H   LINE      CODE     ---       #180
      010023E8H   LINE      CODE     ---       #181
      010023EAH   LINE      CODE     ---       #182
      010023EDH   LINE      CODE     ---       #183
      010023F0H   LINE      CODE     ---       #184
      010023F0H   LINE      CODE     ---       #185
      010023F2H   LINE      CODE     ---       #186
      010023F3H   LINE      CODE     ---       #187
      010023F8H   LINE      CODE     ---       #188
      010023F9H   LINE      CODE     ---       #189
      010023FAH   LINE      CODE     ---       #190
      010023FBH   LINE      CODE     ---       #191
      010023FCH   LINE      CODE     ---       #192
      010023FDH   LINE      CODE     ---       #193
      010023FEH   LINE      CODE     ---       #194
      01002403H   LINE      CODE     ---       #195
      01002405H   LINE      CODE     ---       #196
      01002406H   LINE      CODE     ---       #198
      01002406H   LINE      CODE     ---       #199
      0100240BH   LINE      CODE     ---       #200
      0100240CH   LINE      CODE     ---       #201
      0100240DH   LINE      CODE     ---       #202
      0100240EH   LINE      CODE     ---       #203
      0100240FH   LINE      CODE     ---       #204
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 53


      01002410H   LINE      CODE     ---       #205
      01002411H   LINE      CODE     ---       #206
      01002416H   LINE      CODE     ---       #207
      01002416H   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      010025E1H   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 54


      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 55


      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010025E1H   BLOCK     CODE     ---       LVL=0
      010025E1H   LINE      CODE     ---       #65
      010025E1H   LINE      CODE     ---       #66
      010025E1H   LINE      CODE     ---       #68
      010025E8H   LINE      CODE     ---       #71
      010025EDH   LINE      CODE     ---       #72
      010025F3H   LINE      CODE     ---       #75
      010025F8H   LINE      CODE     ---       #78
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 56


      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      020000B0H   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      020000AEH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      020000ACH   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000027H.1 PUBLIC    BIT      BIT       Delay_Over
      020000ABH   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      020000A9H   PUBLIC    XDATA    WORD      Delay_Time
      00000027H.0 PUBLIC    BIT      BIT       Get_String_Buff
      020000A7H   PUBLIC    XDATA    INT       Count_Toggle
      020000A5H   PUBLIC    XDATA    WORD      Delay_Time_Count
      020000A3H   PUBLIC    XDATA    WORD      Motor_Speed_Data
      020000A1H   PUBLIC    XDATA    INT       Num
      020000A0H   PUBLIC    XDATA    BYTE      K3_cnt
      0200009FH   PUBLIC    XDATA    BYTE      K2_cnt
      0200009EH   PUBLIC    XDATA    BYTE      K1_cnt
      0200009CH   PUBLIC    XDATA    WORD      longhit_cnt
      00000026H.7 PUBLIC    BIT      BIT       Center_Line_Control
      00000026H.6 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 57


      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 58


      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 59


      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      0100264BH   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      01002018H   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 60


      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 61


      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002018H   BLOCK     CODE     ---       LVL=0
      01002018H   LINE      CODE     ---       #42
      01002018H   LINE      CODE     ---       #43
      01002018H   LINE      CODE     ---       #44
      0100201BH   LINE      CODE     ---       #45
      0100201EH   LINE      CODE     ---       #46
      01002021H   LINE      CODE     ---       #47
      01002024H   LINE      CODE     ---       #48
      01002027H   LINE      CODE     ---       #49
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 62


      0100202AH   LINE      CODE     ---       #50
      0100202DH   LINE      CODE     ---       #51
      01002030H   LINE      CODE     ---       #77
      01002035H   LINE      CODE     ---       #78
      01002038H   LINE      CODE     ---       #79
      0100203FH   LINE      CODE     ---       #81
      01002044H   LINE      CODE     ---       #82
      01002047H   LINE      CODE     ---       #83
      0100204EH   LINE      CODE     ---       #85
      01002053H   LINE      CODE     ---       #86
      01002056H   LINE      CODE     ---       #87
      0100205DH   LINE      CODE     ---       #90
      01002062H   LINE      CODE     ---       #91
      01002065H   LINE      CODE     ---       #92
      0100206CH   LINE      CODE     ---       #94
      01002071H   LINE      CODE     ---       #95
      01002074H   LINE      CODE     ---       #96
      0100207BH   LINE      CODE     ---       #100
      01002080H   LINE      CODE     ---       #101
      01002083H   LINE      CODE     ---       #102
      01002085H   LINE      CODE     ---       #105
      01002087H   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      0100264BH   BLOCK     CODE     ---       LVL=0
      0100264BH   LINE      CODE     ---       #131
      0100264BH   LINE      CODE     ---       #132
      0100264BH   LINE      CODE     ---       #134
      01002651H   LINE      CODE     ---       #135
      01002654H   LINE      CODE     ---       #138
      01002658H   LINE      CODE     ---       #139
      0100265BH   LINE      CODE     ---       #142
      0100265DH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      0100246EH   PUBLIC    CODE     ---       TMR1_Config
      01002444H   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 63


      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 64


      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 65


      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01002444H   BLOCK     CODE     ---       LVL=0
      01002444H   LINE      CODE     ---       #11
      01002444H   LINE      CODE     ---       #12
      01002444H   LINE      CODE     ---       #16
      0100244CH   LINE      CODE     ---       #20
      01002452H   LINE      CODE     ---       #24
      0100245BH   LINE      CODE     ---       #29
      01002460H   LINE      CODE     ---       #34
      01002466H   LINE      CODE     ---       #35
      01002469H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      0100246EH   BLOCK     CODE     ---       LVL=0
      0100246EH   LINE      CODE     ---       #50
      0100246EH   LINE      CODE     ---       #51
      0100246EH   LINE      CODE     ---       #55
      01002477H   LINE      CODE     ---       #59
      0100247EH   LINE      CODE     ---       #63
      01002487H   LINE      CODE     ---       #68
      0100248CH   LINE      CODE     ---       #73
      0100248FH   LINE      CODE     ---       #74
      01002492H   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      0200001FH   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01002154H   PUBLIC    CODE     ---       _UART_Send_String
      01000056H   PUBLIC    CODE     ---       _putchar
      01002338H   PUBLIC    CODE     ---       UART_1_Config
      0100220CH   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 66


      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 67


      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 68


      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100220CH   BLOCK     CODE     ---       LVL=0
      0100220CH   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      BRTValue
      02000013H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      0100220CH   LINE      CODE     ---       #42
      0100220CH   LINE      CODE     ---       #43
      0100220CH   LINE      CODE     ---       #44
      01002216H   LINE      CODE     ---       #45
      0100221EH   LINE      CODE     ---       #143
      01002227H   LINE      CODE     ---       #144
      0100222CH   LINE      CODE     ---       #147
      01002231H   LINE      CODE     ---       #148
      01002236H   LINE      CODE     ---       #152
      01002241H   LINE      CODE     ---       #153
      01002244H   LINE      CODE     ---       #156
      0100224AH   LINE      CODE     ---       #157
      0100224FH   LINE      CODE     ---       #159
      01002254H   LINE      CODE     ---       #160
      01002257H   LINE      CODE     ---       #161
      0100225AH   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      01002338H   BLOCK     CODE     ---       LVL=0
      01002338H   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      BRTValue
      02000013H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01002338H   LINE      CODE     ---       #223
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 69


      01002338H   LINE      CODE     ---       #224
      01002338H   LINE      CODE     ---       #225
      01002342H   LINE      CODE     ---       #226
      0100234AH   LINE      CODE     ---       #324
      01002353H   LINE      CODE     ---       #325
      01002358H   LINE      CODE     ---       #328
      0100235DH   LINE      CODE     ---       #329
      01002362H   LINE      CODE     ---       #333
      0100236DH   LINE      CODE     ---       #334
      01002370H   LINE      CODE     ---       #337
      01002376H   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000056H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005DH   LINE      CODE     ---       #363
      01000060H   LINE      CODE     ---       #364
      01000062H   LINE      CODE     ---       #365
      ---         BLOCKEND  ---      ---       LVL=0
      00000001H   SYMBOL    DATA     ---       s

      01002154H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000020H   SYMBOL    XDATA    ---       String
      02000023H   SYMBOL    XDATA    BYTE      Length
      0100215FH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01002154H   LINE      CODE     ---       #408
      0100215FH   LINE      CODE     ---       #409
      0100215FH   LINE      CODE     ---       #410
      01002161H   LINE      CODE     ---       #411
      0100216BH   LINE      CODE     ---       #412
      0100216BH   LINE      CODE     ---       #413
      0100216EH   LINE      CODE     ---       #414
      0100216EH   LINE      CODE     ---       #415
      01002183H   LINE      CODE     ---       #416
      01002188H   LINE      CODE     ---       #417
      0100218BH   LINE      CODE     ---       #418
      0100218BH   LINE      CODE     ---       #419
      01002190H   LINE      CODE     ---       #420
      01002190H   LINE      CODE     ---       #421
      010021A5H   LINE      CODE     ---       #422
      010021AAH   LINE      CODE     ---       #423
      010021ADH   LINE      CODE     ---       #424
      010021ADH   LINE      CODE     ---       #425
      010021B0H   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ISR
      020000C4H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      020000C2H   PUBLIC    XDATA    INT       Num_Forward_Pulse
      01000037H   PUBLIC    CODE     ---       SPI_IRQHandler
      01000036H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000032H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000031H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000030H   PUBLIC    CODE     ---       EPWM_IRQHandler
      0100002FH   PUBLIC    CODE     ---       Timer4_IRQHandler
      0100002EH   PUBLIC    CODE     ---       Timer3_IRQHandler
      0100002AH   PUBLIC    CODE     ---       ACMP_IRQHandler
      01000029H   PUBLIC    CODE     ---       LSE_IRQHandler
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 70


      01000028H   PUBLIC    CODE     ---       LVD_IRQHandler
      01000027H   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001F2DH   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001EB5H   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000026H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      010021B1H   PUBLIC    CODE     ---       UART0_IRQHandler
      01002692H   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      01001BAAH   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 71


      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 72


      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #70
      0100000AH   LINE      CODE     ---       #73
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 73



      01001BAAH   BLOCK     CODE     ---       LVL=0
      01001BAAH   LINE      CODE     ---       #82
      01001BB9H   LINE      CODE     ---       #84
      01001BBCH   LINE      CODE     ---       #85
      01001BBFH   LINE      CODE     ---       #87
      01001BC1H   LINE      CODE     ---       #88
      01001BCFH   LINE      CODE     ---       #89
      01001BDDH   LINE      CODE     ---       #90
      01001BF2H   LINE      CODE     ---       #92
      01001C0AH   LINE      CODE     ---       #93
      01001C0AH   LINE      CODE     ---       #94
      01001C0AH   LINE      CODE     ---       #95
      01001C0AH   LINE      CODE     ---       #96
      01001C16H   LINE      CODE     ---       #97
      01001C16H   LINE      CODE     ---       #98
      01001C16H   LINE      CODE     ---       #99
      01001C16H   LINE      CODE     ---       #100
      01001C16H   LINE      CODE     ---       #101
      01001C16H   LINE      CODE     ---       #102
      01001C18H   LINE      CODE     ---       #103
      01001C18H   LINE      CODE     ---       #104
      01001C24H   LINE      CODE     ---       #105
      01001C24H   LINE      CODE     ---       #106
      01001C24H   LINE      CODE     ---       #107
      01001C24H   LINE      CODE     ---       #108
      01001C24H   LINE      CODE     ---       #109
      01001C26H   LINE      CODE     ---       #110
      01001C26H   LINE      CODE     ---       #111
      01001C26H   LINE      CODE     ---       #112
      01001C32H   LINE      CODE     ---       #113
      01001C32H   LINE      CODE     ---       #114
      01001C34H   LINE      CODE     ---       #115
      01001C3AH   LINE      CODE     ---       #116
      01001C3AH   LINE      CODE     ---       #117
      01001C3AH   LINE      CODE     ---       #118
      01001C3AH   LINE      CODE     ---       #119
      01001C3AH   LINE      CODE     ---       #120
      01001C3AH   LINE      CODE     ---       #121
      01001C3AH   LINE      CODE     ---       #124
      01001C3DH   LINE      CODE     ---       #125
      01001C3DH   LINE      CODE     ---       #126
      01001C46H   LINE      CODE     ---       #127
      01001C48H   LINE      CODE     ---       #129
      01001C48H   LINE      CODE     ---       #130
      01001C4DH   LINE      CODE     ---       #131
      01001C4DH   LINE      CODE     ---       #134
      01001C5AH   LINE      CODE     ---       #135
      01001C5AH   LINE      CODE     ---       #136
      01001C5CH   LINE      CODE     ---       #137
      01001C6AH   LINE      CODE     ---       #138
      01001C6CH   LINE      CODE     ---       #140
      01001C6CH   LINE      CODE     ---       #141
      01001C6EH   LINE      CODE     ---       #142
      01001C75H   LINE      CODE     ---       #143
      01001C75H   LINE      CODE     ---       #146
      01001C78H   LINE      CODE     ---       #147
      01001C78H   LINE      CODE     ---       #148
      01001C86H   LINE      CODE     ---       #149
      01001C9BH   LINE      CODE     ---       #150
      01001C9BH   LINE      CODE     ---       #151
      01001C9DH   LINE      CODE     ---       #152
      01001C9DH   LINE      CODE     ---       #153
      01001C9DH   LINE      CODE     ---       #154
      01001C9FH   LINE      CODE     ---       #156
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 74


      01001C9FH   LINE      CODE     ---       #157
      01001CA6H   LINE      CODE     ---       #158
      01001CA6H   LINE      CODE     ---       #159
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #168
      01000012H   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

      01002692H   BLOCK     CODE     ---       LVL=0
      01002692H   LINE      CODE     ---       #180
      01002692H   LINE      CODE     ---       #182
      01002695H   LINE      CODE     ---       #183
      01002698H   LINE      CODE     ---       #199
      ---         BLOCKEND  ---      ---       LVL=0

      010021B1H   BLOCK     CODE     ---       LVL=0
      010021B1H   LINE      CODE     ---       #208
      010021CEH   LINE      CODE     ---       #210
      010021D6H   LINE      CODE     ---       #211
      010021D6H   LINE      CODE     ---       #212
      010021D8H   LINE      CODE     ---       #213
      010021DDH   LINE      CODE     ---       #214
      010021ECH   LINE      CODE     ---       #215
      010021F1H   LINE      CODE     ---       #216
      010021F1H   LINE      CODE     ---       #217
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #226
      0100001AH   LINE      CODE     ---       #229
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #238
      01000022H   LINE      CODE     ---       #241
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #250
      01000026H   LINE      CODE     ---       #253
      ---         BLOCKEND  ---      ---       LVL=0

      01001EB5H   BLOCK     CODE     ---       LVL=0
      01001EB5H   LINE      CODE     ---       #262
      01001EC4H   LINE      CODE     ---       #265
      01001EC7H   LINE      CODE     ---       #267
      01001ECAH   LINE      CODE     ---       #268
      01001ECAH   LINE      CODE     ---       #270
      01001ECDH   LINE      CODE     ---       #271
      01001ECDH   LINE      CODE     ---       #272
      01001EDCH   LINE      CODE     ---       #273
      01001EDEH   LINE      CODE     ---       #274
      01001EE0H   LINE      CODE     ---       #275
      01001EE0H   LINE      CODE     ---       #276
      01001EE2H   LINE      CODE     ---       #278
      01001EE2H   LINE      CODE     ---       #279
      01001EE5H   LINE      CODE     ---       #280
      01001EE5H   LINE      CODE     ---       #281
      01001F00H   LINE      CODE     ---       #282
      01001F1CH   LINE      CODE     ---       #283
      01001F1CH   LINE      CODE     ---       #284
      01001F1EH   LINE      CODE     ---       #285
      01001F1EH   LINE      CODE     ---       #286
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 75


      01001F20H   LINE      CODE     ---       #287
      01001F20H   LINE      CODE     ---       #288
      01001F20H   LINE      CODE     ---       #289
      ---         BLOCKEND  ---      ---       LVL=0

      01001F2DH   BLOCK     CODE     ---       LVL=0
      01001F2DH   LINE      CODE     ---       #298
      01001F3CH   LINE      CODE     ---       #301
      01001F3FH   LINE      CODE     ---       #303
      01001F42H   LINE      CODE     ---       #304
      01001F42H   LINE      CODE     ---       #306
      01001F45H   LINE      CODE     ---       #307
      01001F45H   LINE      CODE     ---       #308
      01001F54H   LINE      CODE     ---       #309
      01001F56H   LINE      CODE     ---       #310
      01001F58H   LINE      CODE     ---       #311
      01001F58H   LINE      CODE     ---       #312
      01001F5AH   LINE      CODE     ---       #314
      01001F5AH   LINE      CODE     ---       #315
      01001F5DH   LINE      CODE     ---       #316
      01001F5DH   LINE      CODE     ---       #317
      01001F78H   LINE      CODE     ---       #318
      01001F94H   LINE      CODE     ---       #319
      01001F94H   LINE      CODE     ---       #320
      01001F96H   LINE      CODE     ---       #321
      01001F96H   LINE      CODE     ---       #322
      01001F98H   LINE      CODE     ---       #323
      01001F98H   LINE      CODE     ---       #324
      01001F98H   LINE      CODE     ---       #325
      ---         BLOCKEND  ---      ---       LVL=0

      01000027H   BLOCK     CODE     ---       LVL=0
      01000027H   LINE      CODE     ---       #334
      01000027H   LINE      CODE     ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      01000028H   BLOCK     CODE     ---       LVL=0
      01000028H   LINE      CODE     ---       #346
      01000028H   LINE      CODE     ---       #349
      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #358
      01000029H   LINE      CODE     ---       #361
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #370
      0100002AH   LINE      CODE     ---       #373
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #382
      0100002EH   LINE      CODE     ---       #385
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #394
      0100002FH   LINE      CODE     ---       #397
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #406
      01000030H   LINE      CODE     ---       #409
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 76



      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #418
      01000031H   LINE      CODE     ---       #421
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #430
      01000032H   LINE      CODE     ---       #433
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #442
      01000036H   LINE      CODE     ---       #445
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #454
      01000037H   LINE      CODE     ---       #457
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      0200009BH   PUBLIC    XDATA    BYTE      Data_Length
      0200007BH   PUBLIC    XDATA    ---       UART_Get_String
      01001981H   PUBLIC    CODE     ---       UART_Data_Process
      01001FA5H   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      01002528H   PUBLIC    CODE     ---       UART_Data_Init
      0100269FH   PUBLIC    CODE     ---       Clean_UART_Data_Length
      01002699H   PUBLIC    CODE     ---       Return_UART_Data_Length
      01002508H   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 77


      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 78


      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 79


      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      010026C0H   SYMBOL    CONST    ---       _?ix1000
      010026C3H   SYMBOL    CONST    ---       _?ix1001
      010026C6H   SYMBOL    CONST    ---       _?ix1002

      01002508H   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      01002508H   LINE      CODE     ---       #12
      01002508H   LINE      CODE     ---       #13
      01002508H   LINE      CODE     ---       #14
      01002515H   LINE      CODE     ---       #15
      0100251BH   LINE      CODE     ---       #16
      01002525H   LINE      CODE     ---       #17
      01002525H   LINE      CODE     ---       #18
      01002527H   LINE      CODE     ---       #19
      01002527H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      01002699H   BLOCK     CODE     ---       LVL=0
      01002699H   LINE      CODE     ---       #24
      01002699H   LINE      CODE     ---       #25
      01002699H   LINE      CODE     ---       #26
      0100269EH   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      0100269FH   BLOCK     CODE     ---       LVL=0
      0100269FH   LINE      CODE     ---       #30
      0100269FH   LINE      CODE     ---       #31
      0100269FH   LINE      CODE     ---       #32
      010026A4H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      01002528H   BLOCK     CODE     ---       LVL=0
      01002528H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01002528H   LINE      CODE     ---       #36
      01002528H   LINE      CODE     ---       #37
      01002528H   LINE      CODE     ---       #39
      0100252DH   LINE      CODE     ---       #40
      01002538H   LINE      CODE     ---       #41
      01002538H   LINE      CODE     ---       #42
      01002544H   LINE      CODE     ---       #43
      01002547H   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001FA5H   BLOCK     CODE     ---       LVL=0
      02000011H   SYMBOL    XDATA    BYTE      CMD_No
      01001FAAH   BLOCK     CODE     NEAR LAB  LVL=1
      02000012H   SYMBOL    XDATA    ---       UART_PASS_Data
      02000015H   SYMBOL    XDATA    ---       UART_Error_Data
      02000018H   SYMBOL    XDATA    ---       UART_Clean_Pair
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 80


      ---         BLOCKEND  ---      ---       LVL=1
      01001FA5H   LINE      CODE     ---       #61
      01001FAAH   LINE      CODE     ---       #62
      01001FAAH   LINE      CODE     ---       #63
      01001FBDH   LINE      CODE     ---       #64
      01001FD0H   LINE      CODE     ---       #65
      01001FE3H   LINE      CODE     ---       #66
      01001FF1H   LINE      CODE     ---       #67
      01001FF1H   LINE      CODE     ---       #69
      01001FF1H   LINE      CODE     ---       #70
      01001FF1H   LINE      CODE     ---       #71
      01001FF7H   LINE      CODE     ---       #72
      01001FF7H   LINE      CODE     ---       #73
      01001FF9H   LINE      CODE     ---       #75
      01001FF9H   LINE      CODE     ---       #76
      01001FF9H   LINE      CODE     ---       #77
      01002004H   LINE      CODE     ---       #78
      01002004H   LINE      CODE     ---       #79
      01002006H   LINE      CODE     ---       #81
      01002006H   LINE      CODE     ---       #82
      01002006H   LINE      CODE     ---       #83
      01002017H   LINE      CODE     ---       #86
      01002017H   LINE      CODE     ---       #87
      01002017H   LINE      CODE     ---       #88
      01002017H   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      01001981H   BLOCK     CODE     ---       LVL=0
      01001981H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      01001981H   LINE      CODE     ---       #92
      01001981H   LINE      CODE     ---       #93
      01001981H   LINE      CODE     ---       #96
      01001984H   LINE      CODE     ---       #98
      01001989H   LINE      CODE     ---       #99
      01001989H   LINE      CODE     ---       #100
      0100198CH   LINE      CODE     ---       #101
      0100198FH   LINE      CODE     ---       #102
      0100198FH   LINE      CODE     ---       #104
      0100199AH   LINE      CODE     ---       #105
      0100199AH   LINE      CODE     ---       #106
      010019A9H   LINE      CODE     ---       #107
      010019A9H   LINE      CODE     ---       #109
      010019ABH   LINE      CODE     ---       #110
      010019AEH   LINE      CODE     ---       #111
      010019BCH   LINE      CODE     ---       #112
      010019BCH   LINE      CODE     ---       #114
      010019BEH   LINE      CODE     ---       #115
      010019C1H   LINE      CODE     ---       #116
      010019CFH   LINE      CODE     ---       #117
      010019CFH   LINE      CODE     ---       #119
      010019D1H   LINE      CODE     ---       #120
      010019D4H   LINE      CODE     ---       #121
      010019E5H   LINE      CODE     ---       #122
      010019E5H   LINE      CODE     ---       #124
      010019E7H   LINE      CODE     ---       #125
      010019EAH   LINE      CODE     ---       #126
      010019F8H   LINE      CODE     ---       #127
      010019F8H   LINE      CODE     ---       #129
      010019FAH   LINE      CODE     ---       #130
      010019FDH   LINE      CODE     ---       #131
      01001A0BH   LINE      CODE     ---       #132
      01001A0BH   LINE      CODE     ---       #134
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 81


      01001A0DH   LINE      CODE     ---       #135
      01001A10H   LINE      CODE     ---       #136
      01001A21H   LINE      CODE     ---       #137
      01001A21H   LINE      CODE     ---       #139
      01001A23H   LINE      CODE     ---       #140
      01001A25H   LINE      CODE     ---       #141
      01001A33H   LINE      CODE     ---       #142
      01001A33H   LINE      CODE     ---       #144
      01001A35H   LINE      CODE     ---       #145
      01001A37H   LINE      CODE     ---       #146
      01001A45H   LINE      CODE     ---       #147
      01001A45H   LINE      CODE     ---       #149
      01001A47H   LINE      CODE     ---       #150
      01001A49H   LINE      CODE     ---       #151
      01001A5AH   LINE      CODE     ---       #152
      01001A5AH   LINE      CODE     ---       #154
      01001A5CH   LINE      CODE     ---       #155
      01001A5EH   LINE      CODE     ---       #156
      01001A6CH   LINE      CODE     ---       #157
      01001A6CH   LINE      CODE     ---       #159
      01001A6EH   LINE      CODE     ---       #160
      01001A70H   LINE      CODE     ---       #161
      01001A7EH   LINE      CODE     ---       #162
      01001A7EH   LINE      CODE     ---       #164
      01001A80H   LINE      CODE     ---       #165
      01001A82H   LINE      CODE     ---       #166
      01001A8EH   LINE      CODE     ---       #167
      01001A8EH   LINE      CODE     ---       #169
      01001A90H   LINE      CODE     ---       #170
      01001A92H   LINE      CODE     ---       #172
      01001A92H   LINE      CODE     ---       #174
      01001A94H   LINE      CODE     ---       #175
      01001A94H   LINE      CODE     ---       #176
      01001A94H   LINE      CODE     ---       #178
      01001A97H   LINE      CODE     ---       #180
      01001A99H   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000026H.5 PUBLIC    BIT      BIT       K5_Press
      00000026H.4 PUBLIC    BIT      BIT       K4_Press
      00000026H.3 PUBLIC    BIT      BIT       K3_Press
      00000026H.2 PUBLIC    BIT      BIT       K2_Press
      00000026H.1 PUBLIC    BIT      BIT       K1_Press
      020000C1H   PUBLIC    XDATA    BYTE      K5_Count
      020000C0H   PUBLIC    XDATA    BYTE      K4_Count
      020000BFH   PUBLIC    XDATA    BYTE      K3_Count
      020000BEH   PUBLIC    XDATA    BYTE      K2_Count
      020000BDH   PUBLIC    XDATA    BYTE      K1_Count
      020000BCH   PUBLIC    XDATA    BYTE      Key_Buff
      01002377H   PUBLIC    CODE     ---       Key_Buff_Return
      01001857H   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 82


      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 83


      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 84


      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001857H   BLOCK     CODE     ---       LVL=0
      01001857H   LINE      CODE     ---       #20
      01001857H   LINE      CODE     ---       #21
      01001857H   LINE      CODE     ---       #23
      0100185AH   LINE      CODE     ---       #24
      0100185AH   LINE      CODE     ---       #25
      0100185DH   LINE      CODE     ---       #26
      0100185DH   LINE      CODE     ---       #27
      0100186AH   LINE      CODE     ---       #28
      0100186AH   LINE      CODE     ---       #29
      0100186CH   LINE      CODE     ---       #30
      0100186EH   LINE      CODE     ---       #31
      01001870H   LINE      CODE     ---       #43
      01001870H   LINE      CODE     ---       #44
      01001873H   LINE      CODE     ---       #45
      01001873H   LINE      CODE     ---       #46
      01001880H   LINE      CODE     ---       #47
      01001880H   LINE      CODE     ---       #48
      01001882H   LINE      CODE     ---       #49
      01001884H   LINE      CODE     ---       #50
      01001886H   LINE      CODE     ---       #52
      01001886H   LINE      CODE     ---       #53
      0100188CH   LINE      CODE     ---       #54
      0100188CH   LINE      CODE     ---       #55
      0100188EH   LINE      CODE     ---       #57
      0100188EH   LINE      CODE     ---       #58
      01001893H   LINE      CODE     ---       #59
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 85


      01001893H   LINE      CODE     ---       #60
      01001893H   LINE      CODE     ---       #63
      01001896H   LINE      CODE     ---       #64
      01001896H   LINE      CODE     ---       #65
      01001899H   LINE      CODE     ---       #66
      01001899H   LINE      CODE     ---       #67
      010018A6H   LINE      CODE     ---       #68
      010018A6H   LINE      CODE     ---       #69
      010018A8H   LINE      CODE     ---       #70
      010018AAH   LINE      CODE     ---       #71
      010018ACH   LINE      CODE     ---       #83
      010018ACH   LINE      CODE     ---       #84
      010018AFH   LINE      CODE     ---       #85
      010018AFH   LINE      CODE     ---       #86
      010018BCH   LINE      CODE     ---       #87
      010018BCH   LINE      CODE     ---       #88
      010018BEH   LINE      CODE     ---       #89
      010018C0H   LINE      CODE     ---       #90
      010018C2H   LINE      CODE     ---       #92
      010018C2H   LINE      CODE     ---       #93
      010018C8H   LINE      CODE     ---       #94
      010018C8H   LINE      CODE     ---       #95
      010018CAH   LINE      CODE     ---       #97
      010018CAH   LINE      CODE     ---       #98
      010018CFH   LINE      CODE     ---       #99
      010018CFH   LINE      CODE     ---       #100
      010018CFH   LINE      CODE     ---       #103
      010018D2H   LINE      CODE     ---       #104
      010018D2H   LINE      CODE     ---       #105
      010018D5H   LINE      CODE     ---       #106
      010018D5H   LINE      CODE     ---       #107
      010018E2H   LINE      CODE     ---       #108
      010018E2H   LINE      CODE     ---       #109
      010018E4H   LINE      CODE     ---       #110
      010018E6H   LINE      CODE     ---       #111
      010018E8H   LINE      CODE     ---       #123
      010018E8H   LINE      CODE     ---       #124
      010018EBH   LINE      CODE     ---       #125
      010018EBH   LINE      CODE     ---       #126
      010018F8H   LINE      CODE     ---       #127
      010018F8H   LINE      CODE     ---       #128
      010018FAH   LINE      CODE     ---       #129
      010018FCH   LINE      CODE     ---       #130
      010018FEH   LINE      CODE     ---       #132
      010018FEH   LINE      CODE     ---       #133
      01001904H   LINE      CODE     ---       #134
      01001904H   LINE      CODE     ---       #135
      01001906H   LINE      CODE     ---       #137
      01001906H   LINE      CODE     ---       #138
      0100190BH   LINE      CODE     ---       #139
      0100190BH   LINE      CODE     ---       #140
      0100190BH   LINE      CODE     ---       #143
      0100190EH   LINE      CODE     ---       #144
      0100190EH   LINE      CODE     ---       #145
      01001911H   LINE      CODE     ---       #146
      01001911H   LINE      CODE     ---       #147
      0100191EH   LINE      CODE     ---       #148
      0100191EH   LINE      CODE     ---       #149
      01001920H   LINE      CODE     ---       #150
      01001922H   LINE      CODE     ---       #151
      01001924H   LINE      CODE     ---       #163
      01001924H   LINE      CODE     ---       #164
      01001927H   LINE      CODE     ---       #165
      01001927H   LINE      CODE     ---       #166
      01001934H   LINE      CODE     ---       #167
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 86


      01001934H   LINE      CODE     ---       #168
      01001936H   LINE      CODE     ---       #169
      01001938H   LINE      CODE     ---       #170
      0100193AH   LINE      CODE     ---       #172
      0100193AH   LINE      CODE     ---       #173
      01001940H   LINE      CODE     ---       #174
      01001940H   LINE      CODE     ---       #175
      01001942H   LINE      CODE     ---       #177
      01001942H   LINE      CODE     ---       #178
      01001947H   LINE      CODE     ---       #179
      01001947H   LINE      CODE     ---       #180
      01001947H   LINE      CODE     ---       #183
      0100194AH   LINE      CODE     ---       #184
      0100194AH   LINE      CODE     ---       #185
      0100194DH   LINE      CODE     ---       #186
      0100194DH   LINE      CODE     ---       #187
      0100195AH   LINE      CODE     ---       #188
      0100195AH   LINE      CODE     ---       #189
      0100195CH   LINE      CODE     ---       #190
      0100195EH   LINE      CODE     ---       #191
      0100195FH   LINE      CODE     ---       #203
      0100195FH   LINE      CODE     ---       #204
      01001962H   LINE      CODE     ---       #205
      01001962H   LINE      CODE     ---       #206
      0100196FH   LINE      CODE     ---       #207
      0100196FH   LINE      CODE     ---       #208
      01001971H   LINE      CODE     ---       #209
      01001973H   LINE      CODE     ---       #210
      01001974H   LINE      CODE     ---       #212
      01001974H   LINE      CODE     ---       #213
      0100197AH   LINE      CODE     ---       #214
      0100197AH   LINE      CODE     ---       #215
      0100197BH   LINE      CODE     ---       #217
      0100197BH   LINE      CODE     ---       #218
      01001980H   LINE      CODE     ---       #219
      01001980H   LINE      CODE     ---       #220
      01001980H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      01002377H   BLOCK     CODE     ---       LVL=0
      01002377H   LINE      CODE     ---       #224
      01002377H   LINE      CODE     ---       #225
      01002377H   LINE      CODE     ---       #226
      0100237CH   LINE      CODE     ---       #228
      01002383H   LINE      CODE     ---       #229
      0100238DH   LINE      CODE     ---       #230
      01002397H   LINE      CODE     ---       #231
      010023A1H   LINE      CODE     ---       #232
      010023ABH   LINE      CODE     ---       #234
      010023B0H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      0100171CH   PUBLIC    CODE     ---       _ADC
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 87


      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 88


      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 89


      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100171CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0100171CH   BLOCK     CODE     NEAR LAB  LVL=1
      020000B1H   SYMBOL    XDATA    ---       filter_buffer
      020000BBH   SYMBOL    XDATA    BYTE      filter_index
      02000011H   SYMBOL    XDATA    DWORD     sum
      02000015H   SYMBOL    XDATA    WORD      adc_result
      02000017H   SYMBOL    XDATA    BYTE      i
      02000018H   SYMBOL    XDATA    FLOAT     v_adc
      0200001CH   SYMBOL    XDATA    FLOAT     v_bat
      ---         BLOCKEND  ---      ---       LVL=1
      0100171CH   LINE      CODE     ---       #70
      0100171CH   LINE      CODE     ---       #71
      0100171CH   LINE      CODE     ---       #75
      01001726H   LINE      CODE     ---       #81
      01001728H   LINE      CODE     ---       #82
      0100172BH   LINE      CODE     ---       #84
      0100172EH   LINE      CODE     ---       #85
      01001733H   LINE      CODE     ---       #86
      0100173EH   LINE      CODE     ---       #89
      01001753H   LINE      CODE     ---       #90
      01001766H   LINE      CODE     ---       #91
      01001778H   LINE      CODE     ---       #92
      01001778H   LINE      CODE     ---       #93
      010017AFH   LINE      CODE     ---       #94
      010017B7H   LINE      CODE     ---       #95
      010017D6H   LINE      CODE     ---       #98
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 90


      010017F9H   LINE      CODE     ---       #99
      01001822H   LINE      CODE     ---       #101
      0100184EH   LINE      CODE     ---       #102
      01001856H   LINE      CODE     ---       #103
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.3 PUBLIC    BIT      BIT       speedup
      00000024H.2 PUBLIC    BIT      BIT       longhit
      0200007AH   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.1 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.0 PUBLIC    BIT      BIT       direction_changed
      00000023H.7 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000078H   PUBLIC    XDATA    WORD      BatV
      00000023H.6 PUBLIC    BIT      BIT       key3_long_started
      00000023H.5 PUBLIC    BIT      BIT       key1_long_started
      00000023H.4 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.3 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.2 PUBLIC    BIT      BIT       Delay_Open
      02000077H   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.1 PUBLIC    BIT      BIT       charge_flash
      02000076H   PUBLIC    XDATA    BYTE      last_direction
      00000023H.0 PUBLIC    BIT      BIT       key3_pressed
      00000022H.7 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000022H.6 PUBLIC    BIT      BIT       key1_pressed
      02000075H   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.5 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.4 PUBLIC    BIT      BIT       ledonoff
      02000073H   PUBLIC    XDATA    WORD      original_speed
      00000022H.3 PUBLIC    BIT      BIT       key3_handle
      02000072H   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000070H   PUBLIC    XDATA    INT       Self_Check
      00000022H.2 PUBLIC    BIT      BIT       key1_handle
      0200006EH   PUBLIC    XDATA    WORD      key3_duration
      0200006CH   PUBLIC    XDATA    WORD      dly
      00000022H.1 PUBLIC    BIT      BIT       k3_released
      0200006AH   PUBLIC    XDATA    WORD      key1_duration
      00000022H.0 PUBLIC    BIT      BIT       k2_released
      02000068H   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000021H.7 PUBLIC    BIT      BIT       batlow1
      02000066H   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000021H.6 PUBLIC    BIT      BIT       auto_rotate_mode
      02000065H   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.5 PUBLIC    BIT      BIT       Charg_State_Buff
      00000021H.4 PUBLIC    BIT      BIT       led_flash_state
      02000063H   PUBLIC    XDATA    WORD      led_flash_timer
      02000062H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      00000021H.3 PUBLIC    BIT      BIT       need_led_flash
      02000060H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      0200005EH   PUBLIC    XDATA    WORD      timer_1ms_count
      0200005CH   PUBLIC    XDATA    WORD      speedup_cnt
      0200005AH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.2 PUBLIC    BIT      BIT       auto_rotate_flash
      02000058H   PUBLIC    XDATA    WORD      key1_press_time
      00000021H.1 PUBLIC    BIT      BIT       K3_cnt_EN
      02000054H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.0 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.6 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.5 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000052H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 91


      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010015B1H   PUBLIC    CODE     ---       Key_Interrupt_Process
      01001A9AH   PUBLIC    CODE     ---       LED_Control
      01002417H   PUBLIC    CODE     ---       _Store_dly
      01002088H   PUBLIC    CODE     ---       Battery_Check
      010022F1H   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001E22H   PUBLIC    CODE     ---       _Motor_Step_Control
      01002548H   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 92


      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 93


      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000024H.4 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000000H   SYMBOL    XDATA    INT       Key_Input
      02000002H   SYMBOL    XDATA    INT       Charge_Input
      02000004H   SYMBOL    XDATA    INT       Key_State
      02000006H   SYMBOL    XDATA    INT       Key_State_Save
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 94


      02000008H   SYMBOL    XDATA    INT       Charge_State_Save
      0200000AH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000024H.5 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000024H.6 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      0200000CH   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      0200000EH   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000024H.7 SYMBOL    BIT      BIT       Voltage_Low
      0200000FH   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #138
      010000B6H   LINE      CODE     ---       #139
      010000B6H   LINE      CODE     ---       #145
      010000B8H   LINE      CODE     ---       #148
      010000BAH   LINE      CODE     ---       #149
      010000C1H   LINE      CODE     ---       #152
      010000C4H   LINE      CODE     ---       #153
      010000CBH   LINE      CODE     ---       #156
      010000CEH   LINE      CODE     ---       #159
      010000D1H   LINE      CODE     ---       #160
      010000D4H   LINE      CODE     ---       #163
      010000D7H   LINE      CODE     ---       #164
      010000DAH   LINE      CODE     ---       #166
      010000DCH   LINE      CODE     ---       #167
      010000E2H   LINE      CODE     ---       #168
      010000EBH   LINE      CODE     ---       #169
      010000EEH   LINE      CODE     ---       #173
      010000F0H   LINE      CODE     ---       #174
      010000F7H   LINE      CODE     ---       #175
      010000FBH   LINE      CODE     ---       #176
      010000FDH   LINE      CODE     ---       #177
      01000103H   LINE      CODE     ---       #178
      01000109H   LINE      CODE     ---       #179
      0100010FH   LINE      CODE     ---       #180
      01000111H   LINE      CODE     ---       #184
      01000114H   LINE      CODE     ---       #186
      0100011AH   LINE      CODE     ---       #187
      0100011AH   LINE      CODE     ---       #188
      0100011DH   LINE      CODE     ---       #189
      0100011FH   LINE      CODE     ---       #191
      01000122H   LINE      CODE     ---       #192
      0100012DH   LINE      CODE     ---       #195
      01000133H   LINE      CODE     ---       #196
      01000133H   LINE      CODE     ---       #197
      01000141H   LINE      CODE     ---       #198
      01000153H   LINE      CODE     ---       #199
      01000153H   LINE      CODE     ---       #200
      01000157H   LINE      CODE     ---       #201
      01000159H   LINE      CODE     ---       #202
      0100015FH   LINE      CODE     ---       #203
      01000161H   LINE      CODE     ---       #204
      01000163H   LINE      CODE     ---       #205
      01000165H   LINE      CODE     ---       #206
      0100016CH   LINE      CODE     ---       #207
      01000172H   LINE      CODE     ---       #208
      01000174H   LINE      CODE     ---       #209
      01000176H   LINE      CODE     ---       #210
      0100017EH   LINE      CODE     ---       #213
      01000180H   LINE      CODE     ---       #214
      01000182H   LINE      CODE     ---       #215
      01000184H   LINE      CODE     ---       #216
      01000184H   LINE      CODE     ---       #217
      01000187H   LINE      CODE     ---       #219
      01000187H   LINE      CODE     ---       #220
      0100018EH   LINE      CODE     ---       #222
      010001AAH   LINE      CODE     ---       #223
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 95


      010001AAH   LINE      CODE     ---       #224
      010001B0H   LINE      CODE     ---       #225
      010001B0H   LINE      CODE     ---       #226
      010001BEH   LINE      CODE     ---       #227
      010001CFH   LINE      CODE     ---       #228
      010001CFH   LINE      CODE     ---       #229
      010001D3H   LINE      CODE     ---       #230
      010001D5H   LINE      CODE     ---       #231
      010001D7H   LINE      CODE     ---       #232
      010001DCH   LINE      CODE     ---       #233
      010001DCH   LINE      CODE     ---       #234
      010001DEH   LINE      CODE     ---       #235
      010001E9H   LINE      CODE     ---       #236
      010001E9H   LINE      CODE     ---       #237
      010001F7H   LINE      CODE     ---       #238
      01000208H   LINE      CODE     ---       #239
      01000208H   LINE      CODE     ---       #240
      0100020CH   LINE      CODE     ---       #241
      0100020EH   LINE      CODE     ---       #242
      01000212H   LINE      CODE     ---       #243
      01000214H   LINE      CODE     ---       #244
      0100021AH   LINE      CODE     ---       #245
      0100021AH   LINE      CODE     ---       #246
      0100021CH   LINE      CODE     ---       #247
      01000226H   LINE      CODE     ---       #248
      01000226H   LINE      CODE     ---       #249
      0100022CH   LINE      CODE     ---       #250
      0100022EH   LINE      CODE     ---       #251
      01000232H   LINE      CODE     ---       #252
      01000238H   LINE      CODE     ---       #253
      0100023AH   LINE      CODE     ---       #254
      0100023AH   LINE      CODE     ---       #255
      0100023CH   LINE      CODE     ---       #257
      01000246H   LINE      CODE     ---       #258
      01000246H   LINE      CODE     ---       #259
      0100024BH   LINE      CODE     ---       #260
      0100024BH   LINE      CODE     ---       #261
      0100024BH   LINE      CODE     ---       #263
      0100025AH   LINE      CODE     ---       #264
      01000272H   LINE      CODE     ---       #265
      01000275H   LINE      CODE     ---       #269
      0100027EH   LINE      CODE     ---       #270
      0100027EH   LINE      CODE     ---       #272
      01000284H   LINE      CODE     ---       #273
      01000284H   LINE      CODE     ---       #275
      01000284H   LINE      CODE     ---       #277
      01000284H   LINE      CODE     ---       #279
      01000284H   LINE      CODE     ---       #280
      01000284H   LINE      CODE     ---       #281
      01000287H   LINE      CODE     ---       #282
      01000289H   LINE      CODE     ---       #285
      0100028FH   LINE      CODE     ---       #286
      0100028FH   LINE      CODE     ---       #288
      01000295H   LINE      CODE     ---       #289
      01000297H   LINE      CODE     ---       #290
      01000299H   LINE      CODE     ---       #291
      0100029FH   LINE      CODE     ---       #292
      010002A1H   LINE      CODE     ---       #293
      010002A7H   LINE      CODE     ---       #294
      010002A7H   LINE      CODE     ---       #296
      010002ADH   LINE      CODE     ---       #297
      010002AFH   LINE      CODE     ---       #298
      010002B1H   LINE      CODE     ---       #299
      010002B3H   LINE      CODE     ---       #300
      010002B5H   LINE      CODE     ---       #302
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 96


      010002B5H   LINE      CODE     ---       #304
      010002BBH   LINE      CODE     ---       #305
      010002BDH   LINE      CODE     ---       #306
      010002BFH   LINE      CODE     ---       #307
      010002BFH   LINE      CODE     ---       #310
      010002C2H   LINE      CODE     ---       #311
      010002CDH   LINE      CODE     ---       #313
      010002DEH   LINE      CODE     ---       #314
      010002F6H   LINE      CODE     ---       #316
      010002F9H   LINE      CODE     ---       #318
      010002FCH   LINE      CODE     ---       #321
      01000309H   LINE      CODE     ---       #322
      01000309H   LINE      CODE     ---       #324
      0100030CH   LINE      CODE     ---       #325
      0100030CH   LINE      CODE     ---       #327
      0100030FH   LINE      CODE     ---       #328
      0100030FH   LINE      CODE     ---       #329
      01000317H   LINE      CODE     ---       #330
      01000319H   LINE      CODE     ---       #331
      01000319H   LINE      CODE     ---       #332
      01000319H   LINE      CODE     ---       #333
      0100031BH   LINE      CODE     ---       #334
      0100031DH   LINE      CODE     ---       #335
      01000323H   LINE      CODE     ---       #336
      01000323H   LINE      CODE     ---       #338
      01000326H   LINE      CODE     ---       #339
      01000326H   LINE      CODE     ---       #341
      01000329H   LINE      CODE     ---       #342
      01000329H   LINE      CODE     ---       #343
      01000331H   LINE      CODE     ---       #344
      01000333H   LINE      CODE     ---       #345
      01000333H   LINE      CODE     ---       #346
      01000333H   LINE      CODE     ---       #347
      01000335H   LINE      CODE     ---       #348
      01000337H   LINE      CODE     ---       #350
      01000337H   LINE      CODE     ---       #352
      0100033DH   LINE      CODE     ---       #353
      0100033DH   LINE      CODE     ---       #355
      01000345H   LINE      CODE     ---       #356
      01000347H   LINE      CODE     ---       #360
      01000347H   LINE      CODE     ---       #361
      01000349H   LINE      CODE     ---       #362
      01000349H   LINE      CODE     ---       #365
      01000354H   LINE      CODE     ---       #366
      01000354H   LINE      CODE     ---       #368
      01000357H   LINE      CODE     ---       #369
      01000357H   LINE      CODE     ---       #371
      0100035AH   LINE      CODE     ---       #372
      0100035AH   LINE      CODE     ---       #373
      0100035CH   LINE      CODE     ---       #374
      0100035CH   LINE      CODE     ---       #376
      0100035FH   LINE      CODE     ---       #377
      0100035FH   LINE      CODE     ---       #378
      01000361H   LINE      CODE     ---       #379
      01000361H   LINE      CODE     ---       #382
      01000367H   LINE      CODE     ---       #383
      01000367H   LINE      CODE     ---       #384
      01000369H   LINE      CODE     ---       #385
      0100036BH   LINE      CODE     ---       #386
      0100036DH   LINE      CODE     ---       #387
      01000375H   LINE      CODE     ---       #388
      01000377H   LINE      CODE     ---       #390
      01000377H   LINE      CODE     ---       #391
      01000379H   LINE      CODE     ---       #393
      01000379H   LINE      CODE     ---       #397
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 97


      01000385H   LINE      CODE     ---       #398
      01000385H   LINE      CODE     ---       #399
      01000393H   LINE      CODE     ---       #400
      010003A2H   LINE      CODE     ---       #401
      010003A2H   LINE      CODE     ---       #402
      010003A4H   LINE      CODE     ---       #403
      010003A9H   LINE      CODE     ---       #404
      010003ABH   LINE      CODE     ---       #405
      010003ADH   LINE      CODE     ---       #406
      010003AFH   LINE      CODE     ---       #407
      010003B7H   LINE      CODE     ---       #408
      010003B9H   LINE      CODE     ---       #409
      010003BFH   LINE      CODE     ---       #410
      010003BFH   LINE      CODE     ---       #411
      010003C1H   LINE      CODE     ---       #413
      010003C1H   LINE      CODE     ---       #414
      010003C8H   LINE      CODE     ---       #415
      010003CAH   LINE      CODE     ---       #416
      010003CAH   LINE      CODE     ---       #417
      010003CAH   LINE      CODE     ---       #420
      010003D0H   LINE      CODE     ---       #421
      010003D0H   LINE      CODE     ---       #422
      010003EDH   LINE      CODE     ---       #423
      010003EDH   LINE      CODE     ---       #424
      010003F0H   LINE      CODE     ---       #425
      010003F0H   LINE      CODE     ---       #427
      010003F0H   LINE      CODE     ---       #428
      010003F0H   LINE      CODE     ---       #429
      010003F0H   LINE      CODE     ---       #430
      010003F0H   LINE      CODE     ---       #431
      010003F0H   LINE      CODE     ---       #432
      010003F0H   LINE      CODE     ---       #433
      010003F2H   LINE      CODE     ---       #434
      010003FDH   LINE      CODE     ---       #435
      010003FDH   LINE      CODE     ---       #437
      010003FDH   LINE      CODE     ---       #438
      010003FDH   LINE      CODE     ---       #439
      010003FDH   LINE      CODE     ---       #440
      010003FDH   LINE      CODE     ---       #442
      010003FFH   LINE      CODE     ---       #443
      01000405H   LINE      CODE     ---       #444
      01000405H   LINE      CODE     ---       #446
      0100040AH   LINE      CODE     ---       #447
      0100040AH   LINE      CODE     ---       #448
      0100040AH   LINE      CODE     ---       #449
      0100040AH   LINE      CODE     ---       #450
      0100040AH   LINE      CODE     ---       #451
      0100040AH   LINE      CODE     ---       #452
      0100040AH   LINE      CODE     ---       #453
      0100040CH   LINE      CODE     ---       #454
      01000429H   LINE      CODE     ---       #455
      01000429H   LINE      CODE     ---       #456
      0100042CH   LINE      CODE     ---       #457
      0100042CH   LINE      CODE     ---       #459
      01000431H   LINE      CODE     ---       #460
      01000431H   LINE      CODE     ---       #461
      01000431H   LINE      CODE     ---       #462
      01000431H   LINE      CODE     ---       #463
      01000431H   LINE      CODE     ---       #464
      01000431H   LINE      CODE     ---       #465
      01000433H   LINE      CODE     ---       #466
      0100043EH   LINE      CODE     ---       #467
      0100043EH   LINE      CODE     ---       #469
      01000440H   LINE      CODE     ---       #470
      01000442H   LINE      CODE     ---       #471
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 98


      0100044AH   LINE      CODE     ---       #472
      0100044CH   LINE      CODE     ---       #474
      0100044EH   LINE      CODE     ---       #475
      01000454H   LINE      CODE     ---       #476
      01000454H   LINE      CODE     ---       #478
      0100045AH   LINE      CODE     ---       #479
      01000463H   LINE      CODE     ---       #480
      01000465H   LINE      CODE     ---       #481
      01000467H   LINE      CODE     ---       #482
      01000469H   LINE      CODE     ---       #483
      01000470H   LINE      CODE     ---       #484
      01000470H   LINE      CODE     ---       #485
      01000470H   LINE      CODE     ---       #486
      01000470H   LINE      CODE     ---       #489
      01000476H   LINE      CODE     ---       #490
      01000476H   LINE      CODE     ---       #491
      01000478H   LINE      CODE     ---       #492
      01000486H   LINE      CODE     ---       #494
      01000497H   LINE      CODE     ---       #495
      01000497H   LINE      CODE     ---       #496
      0100049FH   LINE      CODE     ---       #497
      010004A1H   LINE      CODE     ---       #499
      010004A1H   LINE      CODE     ---       #500
      010004A8H   LINE      CODE     ---       #501
      010004A8H   LINE      CODE     ---       #502
      010004A8H   LINE      CODE     ---       #505
      010004ABH   LINE      CODE     ---       #506
      010004ABH   LINE      CODE     ---       #508
      010004B9H   LINE      CODE     ---       #509
      010004CBH   LINE      CODE     ---       #510
      010004CBH   LINE      CODE     ---       #511
      010004CFH   LINE      CODE     ---       #512
      010004D1H   LINE      CODE     ---       #513
      010004D1H   LINE      CODE     ---       #514
      010004D4H   LINE      CODE     ---       #516
      010004D4H   LINE      CODE     ---       #518
      010004D6H   LINE      CODE     ---       #519
      010004DDH   LINE      CODE     ---       #520
      010004DDH   LINE      CODE     ---       #521
      010004E0H   LINE      CODE     ---       #524
      010004E0H   LINE      CODE     ---       #526
      010004E3H   LINE      CODE     ---       #529
      01000503H   LINE      CODE     ---       #530
      01000503H   LINE      CODE     ---       #531
      01000505H   LINE      CODE     ---       #532
      01000507H   LINE      CODE     ---       #534
      01000524H   LINE      CODE     ---       #535
      01000524H   LINE      CODE     ---       #536
      0100052CH   LINE      CODE     ---       #537
      0100052EH   LINE      CODE     ---       #538
      0100053EH   LINE      CODE     ---       #539
      0100053EH   LINE      CODE     ---       #540
      0100054CH   LINE      CODE     ---       #541
      0100055DH   LINE      CODE     ---       #542
      0100055DH   LINE      CODE     ---       #543
      01000561H   LINE      CODE     ---       #544
      01000564H   LINE      CODE     ---       #545
      01000566H   LINE      CODE     ---       #546
      01000568H   LINE      CODE     ---       #547
      0100056BH   LINE      CODE     ---       #548
      0100056BH   LINE      CODE     ---       #549
      0100056BH   LINE      CODE     ---       #550
      0100056BH   LINE      CODE     ---       #552
      0100057AH   LINE      CODE     ---       #555
      01000580H   LINE      CODE     ---       #556
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 99


      01000580H   LINE      CODE     ---       #557
      0100058DH   LINE      CODE     ---       #558
      0100058DH   LINE      CODE     ---       #559
      0100058FH   LINE      CODE     ---       #560
      01000591H   LINE      CODE     ---       #561
      01000599H   LINE      CODE     ---       #562
      0100059BH   LINE      CODE     ---       #564
      0100059BH   LINE      CODE     ---       #565
      010005A1H   LINE      CODE     ---       #566
      010005A1H   LINE      CODE     ---       #568
      010005A9H   LINE      CODE     ---       #569
      010005A9H   LINE      CODE     ---       #570
      010005AAH   LINE      CODE     ---       #571
      010005AEH   LINE      CODE     ---       #572
      010005B1H   LINE      CODE     ---       #573
      010005BAH   LINE      CODE     ---       #574
      010005BAH   LINE      CODE     ---       #575
      010005BFH   LINE      CODE     ---       #577
      01000608H   LINE      CODE     ---       #578
      01000608H   LINE      CODE     ---       #579
      01000608H   LINE      CODE     ---       #580
      01000608H   LINE      CODE     ---       #581
      01000616H   LINE      CODE     ---       #582
      01000623H   LINE      CODE     ---       #583
      01000623H   LINE      CODE     ---       #584
      0100062AH   LINE      CODE     ---       #585
      0100062CH   LINE      CODE     ---       #587
      0100062CH   LINE      CODE     ---       #588
      0100062EH   LINE      CODE     ---       #589
      01000635H   LINE      CODE     ---       #590
      01000635H   LINE      CODE     ---       #591
      01000635H   LINE      CODE     ---       #592
      01000635H   LINE      CODE     ---       #593
      01000635H   LINE      CODE     ---       #594
      01000637H   LINE      CODE     ---       #595
      01000637H   LINE      CODE     ---       #596
      01000640H   LINE      CODE     ---       #597
      01000648H   LINE      CODE     ---       #598
      01000648H   LINE      CODE     ---       #599
      0100064AH   LINE      CODE     ---       #600
      0100064AH   LINE      CODE     ---       #601
      0100064AH   LINE      CODE     ---       #602
      0100064AH   LINE      CODE     ---       #603
      0100064AH   LINE      CODE     ---       #604
      0100064AH   LINE      CODE     ---       #605
      0100064CH   LINE      CODE     ---       #606
      0100064CH   LINE      CODE     ---       #607
      0100064CH   LINE      CODE     ---       #608
      0100065AH   LINE      CODE     ---       #609
      01000667H   LINE      CODE     ---       #610
      01000667H   LINE      CODE     ---       #611
      0100066DH   LINE      CODE     ---       #612
      0100066FH   LINE      CODE     ---       #614
      0100066FH   LINE      CODE     ---       #615
      01000671H   LINE      CODE     ---       #616
      01000678H   LINE      CODE     ---       #617
      01000678H   LINE      CODE     ---       #618
      01000683H   LINE      CODE     ---       #619
      01000688H   LINE      CODE     ---       #620
      01000688H   LINE      CODE     ---       #621
      0100068BH   LINE      CODE     ---       #622
      0100068BH   LINE      CODE     ---       #623
      01000694H   LINE      CODE     ---       #624
      0100069CH   LINE      CODE     ---       #625
      0100069CH   LINE      CODE     ---       #626
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 100


      0100069EH   LINE      CODE     ---       #627
      0100069EH   LINE      CODE     ---       #628
      0100069EH   LINE      CODE     ---       #629
      0100069EH   LINE      CODE     ---       #630
      0100069EH   LINE      CODE     ---       #631
      0100069EH   LINE      CODE     ---       #632
      010006A0H   LINE      CODE     ---       #633
      010006A0H   LINE      CODE     ---       #634
      010006A9H   LINE      CODE     ---       #635
      010006B1H   LINE      CODE     ---       #636
      010006B1H   LINE      CODE     ---       #637
      010006B3H   LINE      CODE     ---       #638
      010006B3H   LINE      CODE     ---       #639
      010006B3H   LINE      CODE     ---       #640
      010006B3H   LINE      CODE     ---       #641
      010006B3H   LINE      CODE     ---       #642
      010006B3H   LINE      CODE     ---       #643
      010006B5H   LINE      CODE     ---       #644
      010006B5H   LINE      CODE     ---       #645
      010006BEH   LINE      CODE     ---       #646
      010006C6H   LINE      CODE     ---       #647
      010006C6H   LINE      CODE     ---       #648
      010006C8H   LINE      CODE     ---       #649
      010006C8H   LINE      CODE     ---       #650
      010006CEH   LINE      CODE     ---       #651
      010006D0H   LINE      CODE     ---       #652
      010006D0H   LINE      CODE     ---       #653
      010006D0H   LINE      CODE     ---       #654
      010006D3H   LINE      CODE     ---       #655
      010006D3H   LINE      CODE     ---       #656
      010006D9H   LINE      CODE     ---       #657
      010006DBH   LINE      CODE     ---       #658
      010006E3H   LINE      CODE     ---       #659
      010006E5H   LINE      CODE     ---       #660
      010006E8H   LINE      CODE     ---       #661
      010006E8H   LINE      CODE     ---       #662
      010006E8H   LINE      CODE     ---       #663
      010006F1H   LINE      CODE     ---       #664
      010006F9H   LINE      CODE     ---       #665
      010006F9H   LINE      CODE     ---       #666
      010006FBH   LINE      CODE     ---       #667
      010006FBH   LINE      CODE     ---       #668
      01000701H   LINE      CODE     ---       #669
      01000703H   LINE      CODE     ---       #670
      01000703H   LINE      CODE     ---       #671
      01000703H   LINE      CODE     ---       #672
      01000703H   LINE      CODE     ---       #673
      01000705H   LINE      CODE     ---       #674
      01000705H   LINE      CODE     ---       #675
      01000705H   LINE      CODE     ---       #676
      0100070EH   LINE      CODE     ---       #677
      01000716H   LINE      CODE     ---       #678
      01000716H   LINE      CODE     ---       #679
      01000718H   LINE      CODE     ---       #680
      01000718H   LINE      CODE     ---       #681
      0100071EH   LINE      CODE     ---       #682
      01000720H   LINE      CODE     ---       #683
      01000720H   LINE      CODE     ---       #684
      01000720H   LINE      CODE     ---       #685
      01000720H   LINE      CODE     ---       #686
      01000722H   LINE      CODE     ---       #687
      01000722H   LINE      CODE     ---       #688
      01000722H   LINE      CODE     ---       #689
      0100072BH   LINE      CODE     ---       #690
      01000733H   LINE      CODE     ---       #691
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 101


      01000733H   LINE      CODE     ---       #692
      01000735H   LINE      CODE     ---       #693
      01000735H   LINE      CODE     ---       #694
      0100073BH   LINE      CODE     ---       #695
      0100073DH   LINE      CODE     ---       #696
      0100073DH   LINE      CODE     ---       #697
      0100073DH   LINE      CODE     ---       #698
      0100073DH   LINE      CODE     ---       #699
      0100073FH   LINE      CODE     ---       #700
      0100073FH   LINE      CODE     ---       #701
      0100073FH   LINE      CODE     ---       #702
      01000748H   LINE      CODE     ---       #703
      01000750H   LINE      CODE     ---       #704
      01000750H   LINE      CODE     ---       #705
      01000752H   LINE      CODE     ---       #706
      01000752H   LINE      CODE     ---       #707
      01000758H   LINE      CODE     ---       #708
      0100075AH   LINE      CODE     ---       #709
      0100075CH   LINE      CODE     ---       #710
      01000762H   LINE      CODE     ---       #711
      01000762H   LINE      CODE     ---       #712
      01000764H   LINE      CODE     ---       #713
      01000764H   LINE      CODE     ---       #714
      01000764H   LINE      CODE     ---       #715
      0100076AH   LINE      CODE     ---       #716
      0100076CH   LINE      CODE     ---       #717
      0100076CH   LINE      CODE     ---       #718
      0100076CH   LINE      CODE     ---       #719
      0100076CH   LINE      CODE     ---       #720
      0100076CH   LINE      CODE     ---       #721
      0100076CH   LINE      CODE     ---       #722
      0100076CH   LINE      CODE     ---       #723
      0100076CH   LINE      CODE     ---       #726
      0100077DH   LINE      CODE     ---       #727
      0100077DH   LINE      CODE     ---       #728
      0100077FH   LINE      CODE     ---       #729
      01000781H   LINE      CODE     ---       #730
      0100078FH   LINE      CODE     ---       #732
      0100079BH   LINE      CODE     ---       #733
      0100079BH   LINE      CODE     ---       #734
      0100079DH   LINE      CODE     ---       #735
      0100079FH   LINE      CODE     ---       #736
      010007A1H   LINE      CODE     ---       #737
      010007A1H   LINE      CODE     ---       #738
      010007A7H   LINE      CODE     ---       #739
      010007A7H   LINE      CODE     ---       #740
      010007AAH   LINE      CODE     ---       #741
      010007B1H   LINE      CODE     ---       #742
      010007B3H   LINE      CODE     ---       #743
      010007B3H   LINE      CODE     ---       #744
      010007B6H   LINE      CODE     ---       #745
      010007B6H   LINE      CODE     ---       #746
      010007BFH   LINE      CODE     ---       #747
      010007BFH   LINE      CODE     ---       #748
      010007C2H   LINE      CODE     ---       #749
      010007C2H   LINE      CODE     ---       #751
      010007C5H   LINE      CODE     ---       #752
      010007C5H   LINE      CODE     ---       #753
      010007CEH   LINE      CODE     ---       #754
      010007D0H   LINE      CODE     ---       #756
      010007D0H   LINE      CODE     ---       #758
      010007DDH   LINE      CODE     ---       #759
      010007DDH   LINE      CODE     ---       #760
      010007E5H   LINE      CODE     ---       #761
      010007E7H   LINE      CODE     ---       #762
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 102


      010007EDH   LINE      CODE     ---       #763
      010007EDH   LINE      CODE     ---       #764
      010007F5H   LINE      CODE     ---       #765
      010007F5H   LINE      CODE     ---       #766
      010007F5H   LINE      CODE     ---       #767
      010007F5H   LINE      CODE     ---       #770
      010007F8H   LINE      CODE     ---       #771
      010007F8H   LINE      CODE     ---       #773
      010007FEH   LINE      CODE     ---       #774
      010007FEH   LINE      CODE     ---       #776
      0100080FH   LINE      CODE     ---       #777
      0100080FH   LINE      CODE     ---       #778
      0100080FH   LINE      CODE     ---       #779
      01000811H   LINE      CODE     ---       #781
      01000811H   LINE      CODE     ---       #782
      01000819H   LINE      CODE     ---       #783
      01000820H   LINE      CODE     ---       #784
      01000826H   LINE      CODE     ---       #785
      01000828H   LINE      CODE     ---       #786
      0100082AH   LINE      CODE     ---       #787
      0100082CH   LINE      CODE     ---       #788
      0100082EH   LINE      CODE     ---       #789
      01000830H   LINE      CODE     ---       #790
      01000830H   LINE      CODE     ---       #791
      01000833H   LINE      CODE     ---       #793
      01000833H   LINE      CODE     ---       #795
      0100083BH   LINE      CODE     ---       #796
      01000842H   LINE      CODE     ---       #797
      01000848H   LINE      CODE     ---       #798
      0100084AH   LINE      CODE     ---       #799
      0100084CH   LINE      CODE     ---       #800
      0100084EH   LINE      CODE     ---       #801
      01000850H   LINE      CODE     ---       #802
      01000850H   LINE      CODE     ---       #803
      01000853H   LINE      CODE     ---       #804
      01000864H   LINE      CODE     ---       #805
      01000864H   LINE      CODE     ---       #806
      0100086CH   LINE      CODE     ---       #807
      0100086FH   LINE      CODE     ---       #809
      0100086FH   LINE      CODE     ---       #811
      01000875H   LINE      CODE     ---       #812
      01000875H   LINE      CODE     ---       #813
      01000875H   LINE      CODE     ---       #814
      01000875H   LINE      CODE     ---       #815
      01000875H   LINE      CODE     ---       #816
      01000875H   LINE      CODE     ---       #817
      01000875H   LINE      CODE     ---       #818
      01000875H   LINE      CODE     ---       #819
      01000877H   LINE      CODE     ---       #821
      01000877H   LINE      CODE     ---       #824
      0100087DH   LINE      CODE     ---       #825
      0100087DH   LINE      CODE     ---       #826
      01000884H   LINE      CODE     ---       #827
      01000887H   LINE      CODE     ---       #829
      01000887H   LINE      CODE     ---       #831
      0100088FH   LINE      CODE     ---       #832
      01000896H   LINE      CODE     ---       #833
      0100089CH   LINE      CODE     ---       #834
      0100089EH   LINE      CODE     ---       #835
      010008A0H   LINE      CODE     ---       #836
      010008A2H   LINE      CODE     ---       #837
      010008A2H   LINE      CODE     ---       #838
      010008A2H   LINE      CODE     ---       #839
      010008A2H   LINE      CODE     ---       #840
      010008A5H   LINE      CODE     ---       #841
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 103


      010008B0H   LINE      CODE     ---       #842
      010008B0H   LINE      CODE     ---       #843
      010008B2H   LINE      CODE     ---       #844
      010008B4H   LINE      CODE     ---       #845
      010008B6H   LINE      CODE     ---       #846
      010008B8H   LINE      CODE     ---       #847
      010008BAH   LINE      CODE     ---       #848
      010008BDH   LINE      CODE     ---       #849
      010008C6H   LINE      CODE     ---       #850
      010008C6H   LINE      CODE     ---       #852
      010008C6H   LINE      CODE     ---       #853
      010008CDH   LINE      CODE     ---       #854
      010008CDH   LINE      CODE     ---       #855
      010008CFH   LINE      CODE     ---       #856
      010008D1H   LINE      CODE     ---       #857
      010008D3H   LINE      CODE     ---       #858
      010008D5H   LINE      CODE     ---       #859
      010008D7H   LINE      CODE     ---       #860
      010008D9H   LINE      CODE     ---       #861
      010008DBH   LINE      CODE     ---       #864
      010008DDH   LINE      CODE     ---       #865
      010008DFH   LINE      CODE     ---       #866
      010008E1H   LINE      CODE     ---       #867
      010008E7H   LINE      CODE     ---       #868
      010008EDH   LINE      CODE     ---       #869
      010008EFH   LINE      CODE     ---       #870
      010008F1H   LINE      CODE     ---       #871
      010008F3H   LINE      CODE     ---       #872
      010008F5H   LINE      CODE     ---       #875
      010008F8H   LINE      CODE     ---       #878
      010008FBH   LINE      CODE     ---       #881
      010008FEH   LINE      CODE     ---       #884
      01000901H   LINE      CODE     ---       #885
      01000904H   LINE      CODE     ---       #886
      01000907H   LINE      CODE     ---       #887
      0100090AH   LINE      CODE     ---       #888
      0100090DH   LINE      CODE     ---       #889
      01000910H   LINE      CODE     ---       #890
      01000913H   LINE      CODE     ---       #893
      01000919H   LINE      CODE     ---       #894
      01000919H   LINE      CODE     ---       #895
      ---         BLOCKEND  ---      ---       LVL=0

      01002548H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      01002548H   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      01002548H   LINE      CODE     ---       #898
      01002548H   LINE      CODE     ---       #899
      01002548H   LINE      CODE     ---       #901
      01002552H   LINE      CODE     ---       #902
      01002567H   LINE      CODE     ---       #903
      ---         BLOCKEND  ---      ---       LVL=0

      01001E22H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001E22H   BLOCK     CODE     NEAR LAB  LVL=1
      02000050H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      01001E22H   LINE      CODE     ---       #905
      01001E22H   LINE      CODE     ---       #906
      01001E22H   LINE      CODE     ---       #909
      01001E27H   LINE      CODE     ---       #910
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 104


      01001E27H   LINE      CODE     ---       #911
      01001E2DH   LINE      CODE     ---       #912
      01001E37H   LINE      CODE     ---       #913
      01001E37H   LINE      CODE     ---       #914
      01001E39H   LINE      CODE     ---       #915
      01001E39H   LINE      CODE     ---       #916
      01001E3BH   LINE      CODE     ---       #918
      01001E3BH   LINE      CODE     ---       #919
      01001E48H   LINE      CODE     ---       #920
      01001E48H   LINE      CODE     ---       #921
      01001E4BH   LINE      CODE     ---       #922
      01001E4BH   LINE      CODE     ---       #923
      01001E51H   LINE      CODE     ---       #924
      01001E51H   LINE      CODE     ---       #926
      01001E79H   LINE      CODE     ---       #927
      01001E79H   LINE      CODE     ---       #928
      01001E7DH   LINE      CODE     ---       #929
      01001E81H   LINE      CODE     ---       #930
      01001E87H   LINE      CODE     ---       #931
      01001E8DH   LINE      CODE     ---       #932
      01001E96H   LINE      CODE     ---       #933
      01001E9FH   LINE      CODE     ---       #934
      01001EA3H   LINE      CODE     ---       #935
      01001EACH   LINE      CODE     ---       #936
      01001EACH   LINE      CODE     ---       #937
      01001EB4H   LINE      CODE     ---       #939
      01001EB4H   LINE      CODE     ---       #940
      01001EB4H   LINE      CODE     ---       #941
      ---         BLOCKEND  ---      ---       LVL=0

      010022F1H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      010022F1H   LINE      CODE     ---       #943
      010022F1H   LINE      CODE     ---       #944
      010022F1H   LINE      CODE     ---       #945
      01002301H   LINE      CODE     ---       #946
      01002301H   LINE      CODE     ---       #947
      01002301H   LINE      CODE     ---       #948
      0100230AH   LINE      CODE     ---       #949
      01002310H   LINE      CODE     ---       #950
      01002311H   LINE      CODE     ---       #951
      01002311H   LINE      CODE     ---       #952
      01002314H   LINE      CODE     ---       #953
      01002314H   LINE      CODE     ---       #954
      01002319H   LINE      CODE     ---       #955
      0100231AH   LINE      CODE     ---       #957
      0100231AH   LINE      CODE     ---       #958
      01002320H   LINE      CODE     ---       #959
      01002320H   LINE      CODE     ---       #960
      01002321H   LINE      CODE     ---       #961
      01002321H   LINE      CODE     ---       #962
      0100232AH   LINE      CODE     ---       #963
      01002330H   LINE      CODE     ---       #964
      01002331H   LINE      CODE     ---       #965
      01002331H   LINE      CODE     ---       #966
      01002337H   LINE      CODE     ---       #967
      01002337H   LINE      CODE     ---       #968
      01002337H   LINE      CODE     ---       #969
      01002337H   LINE      CODE     ---       #970
      01002337H   LINE      CODE     ---       #971
      ---         BLOCKEND  ---      ---       LVL=0

      01002088H   BLOCK     CODE     ---       LVL=0
      01002088H   BLOCK     CODE     NEAR LAB  LVL=1
      02000051H   SYMBOL    XDATA    BYTE      motor_delay_cnt
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 105


      ---         BLOCKEND  ---      ---       LVL=1
      01002088H   LINE      CODE     ---       #974
      01002088H   LINE      CODE     ---       #975
      01002088H   LINE      CODE     ---       #979
      0100208BH   LINE      CODE     ---       #980
      0100208BH   LINE      CODE     ---       #981
      01002091H   LINE      CODE     ---       #982
      0100209BH   LINE      CODE     ---       #983
      0100209BH   LINE      CODE     ---       #985
      0100209BH   LINE      CODE     ---       #986
      0100209DH   LINE      CODE     ---       #987
      0100209DH   LINE      CODE     ---       #989
      010020AAH   LINE      CODE     ---       #991
      010020B2H   LINE      CODE     ---       #992
      010020B2H   LINE      CODE     ---       #993
      010020B8H   LINE      CODE     ---       #994
      010020C3H   LINE      CODE     ---       #995
      010020C5H   LINE      CODE     ---       #997
      010020C5H   LINE      CODE     ---       #998
      010020C7H   LINE      CODE     ---       #999
      010020CCH   LINE      CODE     ---       #1000
      010020CCH   LINE      CODE     ---       #1002
      010020DBH   LINE      CODE     ---       #1003
      010020DBH   LINE      CODE     ---       #1004
      010020E1H   LINE      CODE     ---       #1005
      010020ECH   LINE      CODE     ---       #1006
      010020EDH   LINE      CODE     ---       #1008
      010020EDH   LINE      CODE     ---       #1009
      010020F2H   LINE      CODE     ---       #1010
      010020F4H   LINE      CODE     ---       #1011
      010020F4H   LINE      CODE     ---       #1012
      ---         BLOCKEND  ---      ---       LVL=0

      01002417H   BLOCK     CODE     ---       LVL=0
      02000011H   SYMBOL    XDATA    WORD      dly1
      01002417H   LINE      CODE     ---       #1014
      0100241FH   LINE      CODE     ---       #1015
      0100241FH   LINE      CODE     ---       #1016
      01002422H   LINE      CODE     ---       #1017
      01002425H   LINE      CODE     ---       #1018
      0100242EH   LINE      CODE     ---       #1019
      0100243DH   LINE      CODE     ---       #1020
      01002440H   LINE      CODE     ---       #1021
      01002443H   LINE      CODE     ---       #1022
      ---         BLOCKEND  ---      ---       LVL=0

      01001A9AH   BLOCK     CODE     ---       LVL=0
      01001A9AH   LINE      CODE     ---       #1037
      01001A9AH   LINE      CODE     ---       #1038
      01001A9AH   LINE      CODE     ---       #1040
      01001AA5H   LINE      CODE     ---       #1041
      01001AA5H   LINE      CODE     ---       #1043
      01001AA8H   LINE      CODE     ---       #1044
      01001AA8H   LINE      CODE     ---       #1046
      01001AAAH   LINE      CODE     ---       #1047
      01001AADH   LINE      CODE     ---       #1048
      01001AADH   LINE      CODE     ---       #1050
      01001AB4H   LINE      CODE     ---       #1051
      01001AB6H   LINE      CODE     ---       #1052
      01001AB8H   LINE      CODE     ---       #1054
      01001AB8H   LINE      CODE     ---       #1055
      01001ABAH   LINE      CODE     ---       #1056
      01001ABAH   LINE      CODE     ---       #1057
      01001ABCH   LINE      CODE     ---       #1059
      01001ABCH   LINE      CODE     ---       #1061
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 106


      01001ABEH   LINE      CODE     ---       #1062
      01001AC1H   LINE      CODE     ---       #1063
      01001AC1H   LINE      CODE     ---       #1065
      01001AC4H   LINE      CODE     ---       #1066
      01001AC4H   LINE      CODE     ---       #1067
      01001AC6H   LINE      CODE     ---       #1068
      01001AC8H   LINE      CODE     ---       #1070
      01001AC8H   LINE      CODE     ---       #1071
      01001ACAH   LINE      CODE     ---       #1072
      01001ACAH   LINE      CODE     ---       #1073
      01001ACCH   LINE      CODE     ---       #1075
      01001ACCH   LINE      CODE     ---       #1077
      01001ACEH   LINE      CODE     ---       #1078
      01001ACEH   LINE      CODE     ---       #1079
      01001ACEH   LINE      CODE     ---       #1080
      01001AD0H   LINE      CODE     ---       #1082
      01001AD0H   LINE      CODE     ---       #1084
      01001AD3H   LINE      CODE     ---       #1085
      01001AD3H   LINE      CODE     ---       #1086
      01001AD5H   LINE      CODE     ---       #1087
      01001AD8H   LINE      CODE     ---       #1088
      01001AD8H   LINE      CODE     ---       #1090
      01001ADFH   LINE      CODE     ---       #1091
      01001AE1H   LINE      CODE     ---       #1092
      01001AE3H   LINE      CODE     ---       #1094
      01001AE3H   LINE      CODE     ---       #1095
      01001AE5H   LINE      CODE     ---       #1096
      01001AE5H   LINE      CODE     ---       #1097
      01001AE7H   LINE      CODE     ---       #1099
      01001AE7H   LINE      CODE     ---       #1100
      01001AEAH   LINE      CODE     ---       #1101
      01001AEAH   LINE      CODE     ---       #1102
      01001AECH   LINE      CODE     ---       #1103
      01001AF6H   LINE      CODE     ---       #1105
      01001AF6H   LINE      CODE     ---       #1106
      01001AFDH   LINE      CODE     ---       #1107
      01001AFFH   LINE      CODE     ---       #1108
      01001AFFH   LINE      CODE     ---       #1109
      01001B01H   LINE      CODE     ---       #1111
      01001B01H   LINE      CODE     ---       #1112
      01001B03H   LINE      CODE     ---       #1113
      01001B0DH   LINE      CODE     ---       #1115
      01001B0DH   LINE      CODE     ---       #1116
      01001B14H   LINE      CODE     ---       #1117
      01001B16H   LINE      CODE     ---       #1118
      01001B16H   LINE      CODE     ---       #1119
      01001B16H   LINE      CODE     ---       #1120
      01001B16H   LINE      CODE     ---       #1121
      01001B16H   LINE      CODE     ---       #1124
      01001B19H   LINE      CODE     ---       #1125
      01001B19H   LINE      CODE     ---       #1126
      01001B27H   LINE      CODE     ---       #1127
      01001B36H   LINE      CODE     ---       #1128
      01001B36H   LINE      CODE     ---       #1129
      01001B38H   LINE      CODE     ---       #1130
      01001B3AH   LINE      CODE     ---       #1132
      01001B3AH   LINE      CODE     ---       #1133
      01001B3CH   LINE      CODE     ---       #1134
      01001B3EH   LINE      CODE     ---       #1135
      01001B45H   LINE      CODE     ---       #1136
      01001B45H   LINE      CODE     ---       #1137
      01001B45H   LINE      CODE     ---       #1140
      01001B4BH   LINE      CODE     ---       #1141
      01001B55H   LINE      CODE     ---       #1142
      01001B55H   LINE      CODE     ---       #1143
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 107


      01001B57H   LINE      CODE     ---       #1144
      01001B5CH   LINE      CODE     ---       #1145
      01001B5CH   LINE      CODE     ---       #1148
      01001B6AH   LINE      CODE     ---       #1149
      01001B79H   LINE      CODE     ---       #1150
      01001B79H   LINE      CODE     ---       #1151
      01001B7DH   LINE      CODE     ---       #1152
      01001B82H   LINE      CODE     ---       #1153
      01001B82H   LINE      CODE     ---       #1156
      01001B8AH   LINE      CODE     ---       #1157
      01001B8AH   LINE      CODE     ---       #1158
      01001B90H   LINE      CODE     ---       #1159
      01001B9AH   LINE      CODE     ---       #1160
      01001B9AH   LINE      CODE     ---       #1161
      01001B9CH   LINE      CODE     ---       #1162
      01001BA1H   LINE      CODE     ---       #1163
      01001BA1H   LINE      CODE     ---       #1164
      01001BA2H   LINE      CODE     ---       #1166
      01001BA2H   LINE      CODE     ---       #1167
      01001BA7H   LINE      CODE     ---       #1168
      01001BA9H   LINE      CODE     ---       #1169
      01001BA9H   LINE      CODE     ---       #1170
      ---         BLOCKEND  ---      ---       LVL=0

      010015B1H   BLOCK     CODE     ---       LVL=0
      010015B1H   LINE      CODE     ---       #1179
      010015B1H   LINE      CODE     ---       #1180
      010015B1H   LINE      CODE     ---       #1182
      010015BDH   LINE      CODE     ---       #1183
      010015BDH   LINE      CODE     ---       #1184
      010015C0H   LINE      CODE     ---       #1185
      010015C0H   LINE      CODE     ---       #1186
      010015C2H   LINE      CODE     ---       #1187
      010015C4H   LINE      CODE     ---       #1188
      010015C6H   LINE      CODE     ---       #1189
      010015C8H   LINE      CODE     ---       #1190
      010015CAH   LINE      CODE     ---       #1191
      010015D0H   LINE      CODE     ---       #1192
      010015D0H   LINE      CODE     ---       #1193
      010015D0H   LINE      CODE     ---       #1196
      010015F1H   LINE      CODE     ---       #1197
      010015F1H   LINE      CODE     ---       #1199
      010015F7H   LINE      CODE     ---       #1200
      010015F7H   LINE      CODE     ---       #1201
      01001600H   LINE      CODE     ---       #1202
      01001606H   LINE      CODE     ---       #1203
      01001608H   LINE      CODE     ---       #1204
      0100160AH   LINE      CODE     ---       #1205
      0100160CH   LINE      CODE     ---       #1206
      0100160EH   LINE      CODE     ---       #1207
      01001612H   LINE      CODE     ---       #1208
      01001612H   LINE      CODE     ---       #1209
      01001612H   LINE      CODE     ---       #1212
      01001615H   LINE      CODE     ---       #1213
      01001615H   LINE      CODE     ---       #1214
      01001617H   LINE      CODE     ---       #1216
      01001632H   LINE      CODE     ---       #1217
      01001632H   LINE      CODE     ---       #1219
      01001634H   LINE      CODE     ---       #1220
      01001636H   LINE      CODE     ---       #1221
      01001638H   LINE      CODE     ---       #1224
      01001641H   LINE      CODE     ---       #1225
      01001647H   LINE      CODE     ---       #1226
      01001649H   LINE      CODE     ---       #1227
      0100164BH   LINE      CODE     ---       #1228
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 108


      0100164FH   LINE      CODE     ---       #1229
      01001656H   LINE      CODE     ---       #1230
      01001658H   LINE      CODE     ---       #1231
      01001667H   LINE      CODE     ---       #1232
      01001667H   LINE      CODE     ---       #1234
      0100166DH   LINE      CODE     ---       #1235
      0100166FH   LINE      CODE     ---       #1236
      01001671H   LINE      CODE     ---       #1237
      01001673H   LINE      CODE     ---       #1238
      01001675H   LINE      CODE     ---       #1239
      01001675H   LINE      CODE     ---       #1240
      01001675H   LINE      CODE     ---       #1243
      01001696H   LINE      CODE     ---       #1244
      01001696H   LINE      CODE     ---       #1246
      0100169CH   LINE      CODE     ---       #1247
      0100169CH   LINE      CODE     ---       #1248
      010016A5H   LINE      CODE     ---       #1249
      010016ABH   LINE      CODE     ---       #1250
      010016ADH   LINE      CODE     ---       #1251
      010016AFH   LINE      CODE     ---       #1252
      010016B1H   LINE      CODE     ---       #1253
      010016B3H   LINE      CODE     ---       #1254
      010016B8H   LINE      CODE     ---       #1255
      010016B8H   LINE      CODE     ---       #1256
      010016B8H   LINE      CODE     ---       #1259
      010016BBH   LINE      CODE     ---       #1260
      010016BBH   LINE      CODE     ---       #1261
      010016BDH   LINE      CODE     ---       #1263
      010016D8H   LINE      CODE     ---       #1264
      010016D8H   LINE      CODE     ---       #1266
      010016DAH   LINE      CODE     ---       #1267
      010016DCH   LINE      CODE     ---       #1268
      010016DEH   LINE      CODE     ---       #1271
      010016E7H   LINE      CODE     ---       #1272
      010016EDH   LINE      CODE     ---       #1273
      010016EFH   LINE      CODE     ---       #1274
      010016F1H   LINE      CODE     ---       #1275
      010016F6H   LINE      CODE     ---       #1276
      010016FDH   LINE      CODE     ---       #1277
      010016FEH   LINE      CODE     ---       #1278
      0100170DH   LINE      CODE     ---       #1279
      0100170DH   LINE      CODE     ---       #1281
      01001713H   LINE      CODE     ---       #1282
      01001715H   LINE      CODE     ---       #1283
      01001717H   LINE      CODE     ---       #1284
      01001719H   LINE      CODE     ---       #1285
      0100171BH   LINE      CODE     ---       #1286
      0100171BH   LINE      CODE     ---       #1287
      0100171BH   LINE      CODE     ---       #1288
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      0100091CH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000A25H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000ACCH   PUBLIC    CODE     ---       ?C?FCASTC
      01000AC7H   PUBLIC    CODE     ---       ?C?FCASTI
      01000AC2H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF
      02000020H   PUBLIC    XDATA    ---       ?_PRINTF?BYTE
      02000020H   PUBLIC    XDATA    ---       ?_SPRINTF?BYTE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 109


      01001199H   PUBLIC    CODE     ---       _PRINTF
      01001193H   PUBLIC    CODE     ---       _SPRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000B00H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000B35H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000B3FH   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000B17H   PUBLIC    CODE     ---       ?C?FPRESULT
      01000B2BH   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000B3CH   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000B4AH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000B87H   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000C93H   PUBLIC    CODE     ---       ?C?FPADD
      01000C8FH   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000DB4H   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      01001DDDH   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000EC4H   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000EEAH   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000F03H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000F30H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000F42H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01000F64H   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      01000FB9H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      0100100BH   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      0100109DH   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      010010ABH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      010010B7H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      010010E8H   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      010010FFH   PUBLIC    CODE     ---       ?C?PSTXDATA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  13:07:08  PAGE 110



      ---         MODULE    ---      ---       ?C?CCASE
      01001108H   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=21.2 xdata=198 const=40 code=9834
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
